import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/notification.dart';

class NotificationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get user notifications
  Future<List<AppNotification>> getUserNotifications(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();

      return querySnapshot.docs
          .map((doc) => AppNotification.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الإشعارات: ${e.toString()}');
    }
  }

  // Create a new notification
  Future<void> createNotification({
    required String userId,
    required String title,
    required String body,
    required NotificationType type,
    Map<String, dynamic>? data,
  }) async {
    try {
      await _firestore.collection('notifications').add({
        'userId': userId,
        'title': title,
        'body': body,
        'type': type.toString().split('.').last,
        'createdAt': FieldValue.serverTimestamp(),
        'isRead': false,
        'data': data,
      });
    } catch (e) {
      throw Exception('فشل في إنشاء الإشعار: ${e.toString()}');
    }
  }

  // Mark notification as read
  Future<void> markAsRead(String userId, String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'isRead': true,
        'readAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في تحديث حالة الإشعار: ${e.toString()}');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      final batch = _firestore.batch();
      for (final doc in querySnapshot.docs) {
        batch.update(doc.reference, {
          'isRead': true,
          'readAt': FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();
    } catch (e) {
      throw Exception('فشل في تحديث الإشعارات: ${e.toString()}');
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).delete();
    } catch (e) {
      throw Exception('فشل في حذف الإشعار: ${e.toString()}');
    }
  }

  // Clear all notifications for user
  Future<void> clearAllNotifications(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .get();

      final batch = _firestore.batch();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      throw Exception('فشل في مسح الإشعارات: ${e.toString()}');
    }
  }

  // Get unread notifications count
  Future<int> getUnreadCount(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  // Send notification to specific user
  Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    required NotificationType type,
    Map<String, dynamic>? data,
  }) async {
    await createNotification(
      userId: userId,
      title: title,
      body: body,
      type: type,
      data: data,
    );
  }

  // Send notification to all users
  Future<void> sendNotificationToAllUsers({
    required String title,
    required String body,
    required NotificationType type,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Get all users
      final usersSnapshot = await _firestore.collection('users').get();
      
      final batch = _firestore.batch();
      for (final userDoc in usersSnapshot.docs) {
        final notificationRef = _firestore.collection('notifications').doc();
        batch.set(notificationRef, {
          'userId': userDoc.id,
          'title': title,
          'body': body,
          'type': type.toString().split('.').last,
          'createdAt': FieldValue.serverTimestamp(),
          'isRead': false,
          'data': data,
        });
      }

      await batch.commit();
    } catch (e) {
      throw Exception('فشل في إرسال الإشعار لجميع المستخدمين: ${e.toString()}');
    }
  }

  // Send book purchase notification
  Future<void> sendBookPurchaseNotification(String userId, String bookTitle) async {
    await sendNotificationToUser(
      userId: userId,
      title: 'تم شراء الكتاب بنجاح',
      body: 'تم شراء "$bookTitle" وإضافته إلى مكتبتك',
      type: NotificationType.bookPurchase,
    );
  }

  // Send new book notification
  Future<void> sendNewBookNotification(String bookTitle, String bookId) async {
    await sendNotificationToAllUsers(
      title: 'كتاب جديد متاح',
      body: 'تم إضافة "$bookTitle" إلى المكتبة',
      type: NotificationType.newBook,
      data: {'bookId': bookId},
    );
  }

  // Send recommendation notification
  Future<void> sendRecommendationNotification(
    String userId,
    String bookTitle,
    String bookId,
  ) async {
    await sendNotificationToUser(
      userId: userId,
      title: 'كتاب مقترح لك',
      body: 'نعتقد أنك ستحب "$bookTitle"',
      type: NotificationType.recommendation,
      data: {'bookId': bookId},
    );
  }

  // Send promotion notification
  Future<void> sendPromotionNotification(
    String title,
    String body, {
    String? url,
  }) async {
    await sendNotificationToAllUsers(
      title: title,
      body: body,
      type: NotificationType.promotion,
      data: url != null ? {'url': url} : null,
    );
  }

  // Send reading reminder notification
  Future<void> sendReadingReminderNotification(String userId) async {
    await sendNotificationToUser(
      userId: userId,
      title: 'تذكير بالقراءة',
      body: 'لم تقرأ منذ فترة، عد إلى كتابك المفضل!',
      type: NotificationType.reminder,
    );
  }

  // Send system notification
  Future<void> sendSystemNotification(String title, String body) async {
    await sendNotificationToAllUsers(
      title: title,
      body: body,
      type: NotificationType.system,
    );
  }

  // Get notifications by type
  Future<List<AppNotification>> getNotificationsByType(
    String userId,
    NotificationType type,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .where('type', isEqualTo: type.toString().split('.').last)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => AppNotification.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الإشعارات: ${e.toString()}');
    }
  }

  // Clean old notifications (older than 30 days)
  Future<void> cleanOldNotifications() async {
    try {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      
      final querySnapshot = await _firestore
          .collection('notifications')
          .where('createdAt', isLessThan: Timestamp.fromDate(thirtyDaysAgo))
          .get();

      final batch = _firestore.batch();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      throw Exception('فشل في تنظيف الإشعارات القديمة: ${e.toString()}');
    }
  }
}
