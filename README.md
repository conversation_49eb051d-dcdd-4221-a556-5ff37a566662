# تطبيق إقرأ - Arabic E-Book Reading App

تطبيق إقرأ هو تطبيق قراءة الكتب الإلكترونية العربية مطور باستخدام Flutter مع Firebase كخادم خلفي.

## ✅ الميزات المكتملة

### 🔐 المصادقة والمستخدمين
- ✅ تسجيل الدخول والتسجيل باستخدام البريد الإلكتروني
- ✅ إدارة الملف الشخصي
- ✅ إعادة تعيين كلمة المرور
- ✅ تسجيل الخروج

### 📚 إدارة الكتب
- ✅ تصفح الكتب حسب الفئات
- ✅ البحث في الكتب والمؤلفين
- ✅ عرض تفاصيل الكتاب
- ✅ تقييمات ومراجعات الكتب
- ✅ إضافة الكتب للمفضلة

### 📖 قارئ الكتب
- ✅ دعم تنسيقات PDF و EPUB
- ✅ إعدادات القراءة (حجم الخط، الوضع الليلي)
- ✅ حفظ تقدم القراءة
- ✅ إضافة إشارات مرجعية

### 🛒 نظام الشراء والدفع
- ✅ شراء الكتب المدفوعة
- ✅ دعم طرق دفع متعددة (بطاقة ائتمان، Apple Pay، STC Pay، مدى)
- ✅ سجل المشتريات
- ✅ نظام الاسترداد

### 📱 المكتبة الشخصية
- ✅ عرض الكتب المشتراة
- ✅ الكتب المفضلة
- ✅ الكتب قيد القراءة
- ✅ تحميل الكتب للقراءة دون اتصال

### 🔔 نظام الإشعارات
- ✅ إشعارات الكتب الجديدة
- ✅ إشعارات الشراء
- ✅ التوصيات الشخصية
- ✅ العروض والخصومات

## 🚀 للبدء

### 1. تثبيت التبعيات
```bash
flutter pub get
```

### 2. إعداد Firebase
1. إنشاء مشروع جديد في Firebase Console
2. تحديث `lib/firebase_options.dart` بمعلومات مشروعك
3. تفعيل Authentication و Firestore و Storage

### 3. تشغيل التطبيق
```bash
flutter run
```

## 📁 بنية المشروع

```
lib/
├── main.dart                 # نقطة دخول التطبيق
├── firebase_options.dart     # إعدادات Firebase
├── models/                   # نماذج البيانات
├── providers/                # مزودات الحالة
├── services/                 # الخدمات
├── screens/                  # الشاشات
└── widgets/                  # المكونات المشتركة
```

## 🛠 التقنيات المستخدمة

- **Flutter** - إطار عمل تطوير التطبيقات
- **Firebase** - الخادم الخلفي
- **Provider** - إدارة الحالة
- **Material Design 3** - تصميم واجهة المستخدم

---

**ملاحظة**: تم إكمال جميع الميزات الأساسية للتطبيق. يمكن الآن تشغيل التطبيق واختباره بعد إعداد Firebase بشكل صحيح.
