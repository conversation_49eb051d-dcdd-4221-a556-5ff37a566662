import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import '../models/user.dart' as app_user;
import '../services/auth_service.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  app_user.User? _user;
  bool _isLoading = false;
  String? _error;

  app_user.User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    _authService.authStateChanges
        .listen((firebase_auth.User? firebaseUser) async {
      if (firebaseUser != null) {
        try {
          _user = await _authService.getUserData(firebaseUser.uid);
          notifyListeners();
        } catch (e) {
          _error = e.toString();
          notifyListeners();
        }
      } else {
        _user = null;
        notifyListeners();
      }
    });
  }

  Future<bool> signIn(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final user =
          await _authService.signInWithEmailAndPassword(email, password);
      if (user != null) {
        _user = user;
        _setLoading(false);
        return true;
      }
      _setLoading(false);
      return false;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  Future<bool> register(String email, String password, String name) async {
    _setLoading(true);
    _clearError();

    try {
      final user = await _authService.registerWithEmailAndPassword(
          email, password, name);
      if (user != null) {
        _user = user;
        _setLoading(false);
        return true;
      }
      _setLoading(false);
      return false;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  Future<void> signOut() async {
    _setLoading(true);
    try {
      await _authService.signOut();
      _user = null;
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.resetPassword(email);
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  Future<void> updateUser(app_user.User updatedUser) async {
    try {
      await _authService.updateUserData(updatedUser);
      _user = updatedUser;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<void> addToFavorites(String bookId) async {
    if (_user == null) return;

    try {
      await _authService.addToFavorites(bookId);
      _user = _user!.copyWith(
        favoriteBooks: [..._user!.favoriteBooks, bookId],
      );
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<void> removeFromFavorites(String bookId) async {
    if (_user == null) return;

    try {
      await _authService.removeFromFavorites(bookId);
      final updatedFavorites =
          _user!.favoriteBooks.where((id) => id != bookId).toList();
      _user = _user!.copyWith(favoriteBooks: updatedFavorites);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<void> addPurchasedBook(String bookId) async {
    if (_user == null) return;

    try {
      await _authService.addPurchasedBook(bookId);
      _user = _user!.copyWith(
        purchasedBooks: [..._user!.purchasedBooks, bookId],
      );
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<void> updateReadingProgress(
      String bookId, Map<String, dynamic> progress) async {
    if (_user == null) return;

    try {
      await _authService.updateReadingProgress(bookId, progress);
      final updatedProgress = Map<String, dynamic>.from(_user!.readingProgress);
      updatedProgress[bookId] = progress;
      _user = _user!.copyWith(readingProgress: updatedProgress);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  bool isFavorite(String bookId) {
    return _user?.favoriteBooks.contains(bookId) ?? false;
  }

  bool isPurchased(String bookId) {
    return _user?.purchasedBooks.contains(bookId) ?? false;
  }

  Map<String, dynamic>? getReadingProgress(String bookId) {
    return _user?.readingProgress[bookId];
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  Future<void> signOut() async {
    try {
      await _authService.signOut();
      _user = null;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }
}
