import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/book_provider.dart';
import '../../widgets/book_card.dart';
import '../../models/book.dart';

class SearchResultsScreen extends StatefulWidget {
  final String query;

  const SearchResultsScreen({
    super.key,
    required this.query,
  });

  @override
  State<SearchResultsScreen> createState() => _SearchResultsScreenState();
}

class _SearchResultsScreenState extends State<SearchResultsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _sortBy = 'relevance';
  BookCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.query;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performSearch();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch() {
    final bookProvider = Provider.of<BookProvider>(context, listen: false);
    bookProvider.searchBooks(_searchController.text.trim());
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية النتائج'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Sort Options
            const Text(
              'ترتيب حسب:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            DropdownButton<String>(
              value: _sortBy,
              isExpanded: true,
              items: const [
                DropdownMenuItem(value: 'relevance', child: Text('الأكثر صلة')),
                DropdownMenuItem(value: 'title', child: Text('العنوان')),
                DropdownMenuItem(value: 'author', child: Text('المؤلف')),
                DropdownMenuItem(value: 'rating', child: Text('التقييم')),
                DropdownMenuItem(value: 'price', child: Text('السعر')),
                DropdownMenuItem(value: 'date', child: Text('تاريخ النشر')),
              ],
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Category Filter
            const Text(
              'التصنيف:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            DropdownButton<BookCategory?>(
              value: _selectedCategory,
              isExpanded: true,
              items: [
                const DropdownMenuItem(value: null, child: Text('جميع التصنيفات')),
                ...BookCategory.values.map((category) => DropdownMenuItem(
                  value: category,
                  child: Text(_getCategoryName(category)),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {});
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  String _getCategoryName(BookCategory category) {
    switch (category) {
      case BookCategory.literature:
        return 'أدب';
      case BookCategory.science:
        return 'علوم';
      case BookCategory.novels:
        return 'روايات';
      case BookCategory.history:
        return 'تاريخ';
      case BookCategory.religion:
        return 'دين';
      case BookCategory.philosophy:
        return 'فلسفة';
      case BookCategory.poetry:
        return 'شعر';
      case BookCategory.children:
        return 'أطفال';
      case BookCategory.biography:
        return 'سيرة ذاتية';
      case BookCategory.selfDevelopment:
        return 'تطوير الذات';
    }
  }

  List<Book> _getFilteredAndSortedBooks(List<Book> books) {
    var filteredBooks = books;
    
    // Apply category filter
    if (_selectedCategory != null) {
      filteredBooks = books.where((book) => book.category == _selectedCategory).toList();
    }
    
    // Apply sorting
    final bookProvider = Provider.of<BookProvider>(context, listen: false);
    return bookProvider.sortBooks(filteredBooks, _sortBy);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نتائج البحث'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              textInputAction: TextInputAction.search,
              onSubmitted: (_) => _performSearch(),
              decoration: InputDecoration(
                hintText: 'ابحث عن كتاب...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: _showFilterDialog,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ),
        ),
      ),
      body: Consumer<BookProvider>(
        builder: (context, bookProvider, child) {
          if (bookProvider.isSearching) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (bookProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ في البحث',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    bookProvider.error!,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _performSearch,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final filteredBooks = _getFilteredAndSortedBooks(bookProvider.searchResults);

          if (filteredBooks.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد نتائج',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'لم نجد أي كتب تطابق بحثك عن "${bookProvider.searchQuery}"',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      _searchController.clear();
                      bookProvider.clearSearch();
                    },
                    child: const Text('مسح البحث'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Results Count
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                color: Colors.grey[100],
                child: Text(
                  'تم العثور على ${filteredBooks.length} كتاب',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              
              // Results Grid
              Expanded(
                child: GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.6,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: filteredBooks.length,
                  itemBuilder: (context, index) {
                    return BookCard(
                      book: filteredBooks[index],
                      width: double.infinity,
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
