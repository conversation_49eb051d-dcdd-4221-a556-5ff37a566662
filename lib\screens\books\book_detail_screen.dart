import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:provider/provider.dart';
import '../../models/book.dart';
import '../../models/review.dart';
import '../../providers/auth_provider.dart';
import '../../providers/book_provider.dart';
import '../../services/book_service.dart';
import '../reader/book_reader_screen.dart';
import '../payment/payment_screen.dart';

class BookDetailScreen extends StatefulWidget {
  final Book book;

  const BookDetailScreen({
    super.key,
    required this.book,
  });

  @override
  State<BookDetailScreen> createState() => _BookDetailScreenState();
}

class _BookDetailScreenState extends State<BookDetailScreen> {
  final BookService _bookService = BookService();
  List<Review> _reviews = [];
  bool _isLoading = true;
  bool _isFavorite = false;
  bool _isPurchased = false;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadBookDetails();
    _checkBookStatus();
  }

  Future<void> _loadBookDetails() async {
    try {
      final reviews = await _bookService.getBookReviews(widget.book.id);
      setState(() {
        _reviews = reviews;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل تفاصيل الكتاب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _checkBookStatus() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.user;
    
    if (user != null) {
      setState(() {
        _isFavorite = user.favoriteBooks.contains(widget.book.id);
        _isPurchased = user.purchasedBooks.contains(widget.book.id);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBookInfo(),
                  const SizedBox(height: 24),
                  _buildActionButtons(),
                  const SizedBox(height: 24),
                  _buildDescription(),
                  const SizedBox(height: 24),
                  _buildBookDetails(),
                  const SizedBox(height: 24),
                  _buildReviewsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            CachedNetworkImage(
              imageUrl: widget.book.coverImage,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey[300],
                child: const Center(child: CircularProgressIndicator()),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[300],
                child: const Icon(Icons.book, size: 80),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            _isFavorite ? Icons.favorite : Icons.favorite_border,
            color: _isFavorite ? Colors.red : Colors.white,
          ),
          onPressed: _toggleFavorite,
        ),
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: _shareBook,
        ),
      ],
    );
  }

  Widget _buildBookInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.book.title,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'بقلم ${widget.book.author}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            RatingBarIndicator(
              rating: widget.book.rating,
              itemBuilder: (context, index) => const Icon(
                Icons.star,
                color: Colors.amber,
              ),
              itemCount: 5,
              itemSize: 20.0,
            ),
            const SizedBox(width: 8),
            Text(
              '${widget.book.rating.toStringAsFixed(1)} (${widget.book.reviewCount} تقييم)',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildInfoChip(
              _getCategoryName(widget.book.category),
              Icons.category,
            ),
            const SizedBox(width: 8),
            _buildInfoChip(
              '${widget.book.pageCount} صفحة',
              Icons.menu_book,
            ),
            const SizedBox(width: 8),
            _buildInfoChip(
              widget.book.format.toString().split('.').last.toUpperCase(),
              Icons.file_present,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: _isPurchased || widget.book.isFree
                ? _readBook
                : _purchaseBook,
            icon: Icon(
              _isPurchased || widget.book.isFree
                  ? Icons.book_online
                  : Icons.shopping_cart,
            ),
            label: Text(
              _isPurchased || widget.book.isFree
                  ? 'قراءة الكتاب'
                  : widget.book.isFree
                      ? 'قراءة مجاناً'
                      : 'شراء بـ ${widget.book.price.toStringAsFixed(0)} ر.س',
            ),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        if (!widget.book.isFree && !_isPurchased)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _previewBook,
              icon: const Icon(Icons.preview),
              label: const Text('معاينة'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'وصف الكتاب',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        AnimatedCrossFade(
          firstChild: Text(
            widget.book.description,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          secondChild: Text(
            widget.book.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          crossFadeState: _isExpanded
              ? CrossFadeState.showSecond
              : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 200),
        ),
        if (widget.book.description.length > 150)
          TextButton(
            onPressed: () => setState(() => _isExpanded = !_isExpanded),
            child: Text(_isExpanded ? 'عرض أقل' : 'عرض المزيد'),
          ),
      ],
    );
  }

  Widget _buildBookDetails() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الكتاب',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildDetailRow('المؤلف', widget.book.author),
            _buildDetailRow('الناشر', widget.book.publisher),
            _buildDetailRow('تاريخ النشر', _formatDate(widget.book.publishedDate)),
            _buildDetailRow('اللغة', widget.book.language == 'ar' ? 'العربية' : 'الإنجليزية'),
            _buildDetailRow('عدد الصفحات', '${widget.book.pageCount} صفحة'),
            _buildDetailRow('التصنيف', _getCategoryName(widget.book.category)),
            _buildDetailRow('التنسيق', widget.book.format.toString().split('.').last.toUpperCase()),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التقييمات والمراجعات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: _writeReview,
              child: const Text('كتابة مراجعة'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (_isLoading)
          const Center(child: CircularProgressIndicator())
        else if (_reviews.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32.0),
              child: Text('لا توجد مراجعات بعد'),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _reviews.length > 3 ? 3 : _reviews.length,
            itemBuilder: (context, index) {
              return _buildReviewCard(_reviews[index]);
            },
          ),
        if (_reviews.length > 3)
          TextButton(
            onPressed: () {
              // TODO: Navigate to all reviews
            },
            child: const Text('عرض جميع المراجعات'),
          ),
      ],
    );
  }

  Widget _buildReviewCard(Review review) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Theme.of(context).primaryColor,
                  child: Text(
                    review.userName.substring(0, 1).toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        review.userName,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      RatingBarIndicator(
                        rating: review.rating,
                        itemBuilder: (context, index) => const Icon(
                          Icons.star,
                          color: Colors.amber,
                        ),
                        itemCount: 5,
                        itemSize: 14.0,
                      ),
                    ],
                  ),
                ),
                Text(
                  _formatDate(review.createdAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            if (review.comment.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(review.comment),
            ],
          ],
        ),
      ),
    );
  }

  String _getCategoryName(BookCategory category) {
    switch (category) {
      case BookCategory.literature:
        return 'أدب';
      case BookCategory.science:
        return 'علوم';
      case BookCategory.novels:
        return 'روايات';
      case BookCategory.history:
        return 'تاريخ';
      case BookCategory.religion:
        return 'دين';
      case BookCategory.philosophy:
        return 'فلسفة';
      case BookCategory.poetry:
        return 'شعر';
      case BookCategory.children:
        return 'أطفال';
      case BookCategory.biography:
        return 'سيرة ذاتية';
      case BookCategory.selfDevelopment:
        return 'تطوير الذات';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _toggleFavorite() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تسجيل الدخول أولاً'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      if (_isFavorite) {
        await _bookService.removeFromFavorites(authProvider.user!.id, widget.book.id);
      } else {
        await _bookService.addToFavorites(authProvider.user!.id, widget.book.id);
      }
      setState(() => _isFavorite = !_isFavorite);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _shareBook() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة المشاركة قيد التطوير')),
    );
  }

  void _readBook() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => BookReaderScreen(book: widget.book),
      ),
    );
  }

  void _purchaseBook() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => PaymentScreen(book: widget.book),
      ),
    );
  }

  void _previewBook() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => BookReaderScreen(
          book: widget.book,
          isPreview: true,
        ),
      ),
    );
  }

  void _writeReview() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تسجيل الدخول لكتابة مراجعة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // TODO: Navigate to write review screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة كتابة المراجعات قيد التطوير')),
    );
  }
}
