import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/book_provider.dart';
import '../../models/book.dart';
import '../../widgets/book_card.dart';
import '../../services/library_service.dart';

class LibraryScreen extends StatefulWidget {
  const LibraryScreen({super.key});

  @override
  State<LibraryScreen> createState() => _LibraryScreenState();
}

class _LibraryScreenState extends State<LibraryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final LibraryService _libraryService = LibraryService();
  
  List<Book> _purchasedBooks = [];
  List<Book> _favoriteBooks = [];
  List<Book> _readingBooks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadLibraryData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadLibraryData() async {
    setState(() => _isLoading = true);
    
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.user;
      
      if (user != null) {
        final results = await Future.wait([
          _libraryService.getPurchasedBooks(user.id),
          _libraryService.getFavoriteBooks(user.id),
          _libraryService.getCurrentlyReadingBooks(user.id),
        ]);
        
        setState(() {
          _purchasedBooks = results[0];
          _favoriteBooks = results[1];
          _readingBooks = results[2];
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المكتبة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مكتبتي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Search in library
            },
          ),
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: _showSortOptions,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              text: 'مشترياتي',
              icon: Icon(Icons.shopping_bag),
            ),
            Tab(
              text: 'المفضلة',
              icon: Icon(Icons.favorite),
            ),
            Tab(
              text: 'قيد القراءة',
              icon: Icon(Icons.book_online),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildPurchasedBooksTab(),
                _buildFavoriteBooksTab(),
                _buildReadingBooksTab(),
              ],
            ),
    );
  }

  Widget _buildPurchasedBooksTab() {
    return _buildBooksGrid(
      _purchasedBooks,
      emptyMessage: 'لم تشتر أي كتب بعد',
      emptyIcon: Icons.shopping_bag_outlined,
    );
  }

  Widget _buildFavoriteBooksTab() {
    return _buildBooksGrid(
      _favoriteBooks,
      emptyMessage: 'لا توجد كتب مفضلة',
      emptyIcon: Icons.favorite_border,
    );
  }

  Widget _buildReadingBooksTab() {
    return _buildBooksGrid(
      _readingBooks,
      emptyMessage: 'لا توجد كتب قيد القراءة',
      emptyIcon: Icons.book_outlined,
      showProgress: true,
    );
  }

  Widget _buildBooksGrid(
    List<Book> books, {
    required String emptyMessage,
    required IconData emptyIcon,
    bool showProgress = false,
  }) {
    if (books.isEmpty) {
      return _buildEmptyState(emptyMessage, emptyIcon);
    }

    return RefreshIndicator(
      onRefresh: _loadLibraryData,
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.6,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: books.length,
        itemBuilder: (context, index) {
          final book = books[index];
          return _buildLibraryBookCard(book, showProgress);
        },
      ),
    );
  }

  Widget _buildLibraryBookCard(Book book, bool showProgress) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.user;
        double? progress;
        
        if (showProgress && user != null) {
          final bookProgress = user.readingProgress[book.id];
          if (bookProgress != null) {
            progress = (bookProgress['currentPage'] ?? 0) / 
                      (bookProgress['totalPages'] ?? book.pageCount);
          }
        }

        return Stack(
          children: [
            BookCard(
              book: book,
              width: double.infinity,
            ),
            
            // Progress indicator for reading books
            if (showProgress && progress != null)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: progress,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(8),
                          bottomRight: Radius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            
            // Context menu
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.more_vert,
                    color: Colors.white,
                    size: 20,
                  ),
                  onPressed: () => _showBookOptions(book),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بتصفح الكتب المتاحة',
            style: TextStyle(color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              // Switch to books tab
              DefaultTabController.of(context)?.animateTo(1);
            },
            child: const Text('تصفح الكتب'),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ترتيب حسب',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSortOption('الأحدث', Icons.access_time),
            _buildSortOption('الأقدم', Icons.history),
            _buildSortOption('الاسم (أ-ي)', Icons.sort_by_alpha),
            _buildSortOption('الاسم (ي-أ)', Icons.sort_by_alpha),
            _buildSortOption('التقييم', Icons.star),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(String title, IconData icon) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      onTap: () {
        Navigator.pop(context);
        // TODO: Implement sorting
      },
    );
  }

  void _showBookOptions(Book book) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.book_online),
              title: const Text('قراءة الكتاب'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Open book reader
              },
            ),
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('تحميل للقراءة دون اتصال'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Download book
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('مشاركة'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Share book
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text(
                'إزالة من المكتبة',
                style: TextStyle(color: Colors.red),
              ),
              onTap: () {
                Navigator.pop(context);
                _confirmRemoveBook(book);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _confirmRemoveBook(Book book) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إزالة الكتاب'),
        content: Text('هل أنت متأكد من رغبتك في إزالة "${book.title}" من مكتبتك؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Remove book from library
            },
            child: const Text(
              'إزالة',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
