import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/book.dart';
import '../services/book_service.dart';

class LibraryService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final BookService _bookService = BookService();

  // Get user's purchased books
  Future<List<Book>> getPurchasedBooks(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return [];
      }

      final userData = userDoc.data()!;
      final purchasedBookIds = List<String>.from(userData['purchasedBooks'] ?? []);

      if (purchasedBookIds.isEmpty) {
        return [];
      }

      // Get book details for purchased books
      final books = <Book>[];
      for (final bookId in purchasedBookIds) {
        try {
          final book = await _bookService.getBookById(bookId);
          if (book != null) {
            books.add(book);
          }
        } catch (e) {
          // Skip books that can't be loaded
          continue;
        }
      }

      return books;
    } catch (e) {
      throw Exception('فشل في جلب الكتب المشتراة: ${e.toString()}');
    }
  }

  // Get user's favorite books
  Future<List<Book>> getFavoriteBooks(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return [];
      }

      final userData = userDoc.data()!;
      final favoriteBookIds = List<String>.from(userData['favoriteBooks'] ?? []);

      if (favoriteBookIds.isEmpty) {
        return [];
      }

      // Get book details for favorite books
      final books = <Book>[];
      for (final bookId in favoriteBookIds) {
        try {
          final book = await _bookService.getBookById(bookId);
          if (book != null) {
            books.add(book);
          }
        } catch (e) {
          // Skip books that can't be loaded
          continue;
        }
      }

      return books;
    } catch (e) {
      throw Exception('فشل في جلب الكتب المفضلة: ${e.toString()}');
    }
  }

  // Get user's currently reading books
  Future<List<Book>> getCurrentlyReadingBooks(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return [];
      }

      final userData = userDoc.data()!;
      final readingProgress = Map<String, dynamic>.from(userData['readingProgress'] ?? {});

      if (readingProgress.isEmpty) {
        return [];
      }

      // Get book details for books with reading progress
      final books = <Book>[];
      for (final bookId in readingProgress.keys) {
        try {
          final book = await _bookService.getBookById(bookId);
          if (book != null) {
            books.add(book);
          }
        } catch (e) {
          // Skip books that can't be loaded
          continue;
        }
      }

      return books;
    } catch (e) {
      throw Exception('فشل في جلب الكتب قيد القراءة: ${e.toString()}');
    }
  }

  // Add book to user's library (after purchase)
  Future<void> addBookToLibrary(String userId, String bookId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'purchasedBooks': FieldValue.arrayUnion([bookId]),
      });
    } catch (e) {
      throw Exception('فشل في إضافة الكتاب للمكتبة: ${e.toString()}');
    }
  }

  // Remove book from user's library
  Future<void> removeBookFromLibrary(String userId, String bookId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'purchasedBooks': FieldValue.arrayRemove([bookId]),
      });
    } catch (e) {
      throw Exception('فشل في إزالة الكتاب من المكتبة: ${e.toString()}');
    }
  }

  // Update reading progress
  Future<void> updateReadingProgress(
    String userId,
    String bookId,
    int currentPage,
    int totalPages,
  ) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'readingProgress.$bookId': {
          'currentPage': currentPage,
          'totalPages': totalPages,
          'lastRead': FieldValue.serverTimestamp(),
          'progress': currentPage / totalPages,
        },
      });
    } catch (e) {
      throw Exception('فشل في تحديث تقدم القراءة: ${e.toString()}');
    }
  }

  // Get reading progress for a specific book
  Future<Map<String, dynamic>?> getReadingProgress(String userId, String bookId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return null;
      }

      final userData = userDoc.data()!;
      final readingProgress = Map<String, dynamic>.from(userData['readingProgress'] ?? {});
      
      return readingProgress[bookId];
    } catch (e) {
      throw Exception('فشل في جلب تقدم القراءة: ${e.toString()}');
    }
  }

  // Mark book as finished
  Future<void> markBookAsFinished(String userId, String bookId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'readingProgress.$bookId.isFinished': true,
        'readingProgress.$bookId.finishedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في تحديث حالة الكتاب: ${e.toString()}');
    }
  }

  // Get user's reading statistics
  Future<Map<String, dynamic>> getReadingStatistics(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return {
          'totalBooks': 0,
          'finishedBooks': 0,
          'currentlyReading': 0,
          'totalPages': 0,
        };
      }

      final userData = userDoc.data()!;
      final purchasedBooks = List<String>.from(userData['purchasedBooks'] ?? []);
      final readingProgress = Map<String, dynamic>.from(userData['readingProgress'] ?? {});

      int finishedBooks = 0;
      int totalPages = 0;

      for (final progress in readingProgress.values) {
        if (progress['isFinished'] == true) {
          finishedBooks++;
        }
        totalPages += (progress['totalPages'] ?? 0) as int;
      }

      return {
        'totalBooks': purchasedBooks.length,
        'finishedBooks': finishedBooks,
        'currentlyReading': readingProgress.length - finishedBooks,
        'totalPages': totalPages,
      };
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات القراءة: ${e.toString()}');
    }
  }

  // Search in user's library
  Future<List<Book>> searchInLibrary(String userId, String query) async {
    try {
      final purchasedBooks = await getPurchasedBooks(userId);
      
      if (query.isEmpty) {
        return purchasedBooks;
      }

      return purchasedBooks.where((book) {
        return book.title.toLowerCase().contains(query.toLowerCase()) ||
               book.author.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      throw Exception('فشل في البحث في المكتبة: ${e.toString()}');
    }
  }

  // Sort library books
  List<Book> sortBooks(List<Book> books, String sortBy) {
    switch (sortBy) {
      case 'title_asc':
        books.sort((a, b) => a.title.compareTo(b.title));
        break;
      case 'title_desc':
        books.sort((a, b) => b.title.compareTo(a.title));
        break;
      case 'author_asc':
        books.sort((a, b) => a.author.compareTo(b.author));
        break;
      case 'author_desc':
        books.sort((a, b) => b.author.compareTo(a.author));
        break;
      case 'date_asc':
        books.sort((a, b) => a.publishedDate.compareTo(b.publishedDate));
        break;
      case 'date_desc':
        books.sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
        break;
      case 'rating_desc':
        books.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      default:
        // Keep original order
        break;
    }
    return books;
  }

  // Get recently read books
  Future<List<Book>> getRecentlyReadBooks(String userId, {int limit = 5}) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return [];
      }

      final userData = userDoc.data()!;
      final readingProgress = Map<String, dynamic>.from(userData['readingProgress'] ?? {});

      // Sort by last read timestamp
      final sortedEntries = readingProgress.entries.toList()
        ..sort((a, b) {
          final aTimestamp = a.value['lastRead'] as Timestamp?;
          final bTimestamp = b.value['lastRead'] as Timestamp?;
          
          if (aTimestamp == null && bTimestamp == null) return 0;
          if (aTimestamp == null) return 1;
          if (bTimestamp == null) return -1;
          
          return bTimestamp.compareTo(aTimestamp);
        });

      // Get book details for recently read books
      final books = <Book>[];
      final bookIds = sortedEntries.take(limit).map((e) => e.key).toList();
      
      for (final bookId in bookIds) {
        try {
          final book = await _bookService.getBookById(bookId);
          if (book != null) {
            books.add(book);
          }
        } catch (e) {
          // Skip books that can't be loaded
          continue;
        }
      }

      return books;
    } catch (e) {
      throw Exception('فشل في جلب الكتب المقروءة مؤخراً: ${e.toString()}');
    }
  }
}
