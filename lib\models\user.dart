class User {
  final String id;
  final String email;
  final String name;
  final String? profileImage;
  final DateTime createdAt;
  final List<String> favoriteBooks;
  final List<String> purchasedBooks;
  final Map<String, dynamic> readingProgress;
  final bool isPremium;

  User({
    required this.id,
    required this.email,
    required this.name,
    this.profileImage,
    required this.createdAt,
    this.favoriteBooks = const [],
    this.purchasedBooks = const [],
    this.readingProgress = const {},
    this.isPremium = false,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      profileImage: json['profileImage'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      favoriteBooks: List<String>.from(json['favoriteBooks'] ?? []),
      purchasedBooks: List<String>.from(json['purchasedBooks'] ?? []),
      readingProgress: Map<String, dynamic>.from(json['readingProgress'] ?? {}),
      isPremium: json['isPremium'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'profileImage': profileImage,
      'createdAt': createdAt.toIso8601String(),
      'favoriteBooks': favoriteBooks,
      'purchasedBooks': purchasedBooks,
      'readingProgress': readingProgress,
      'isPremium': isPremium,
    };
  }

  User copyWith({
    String? id,
    String? email,
    String? name,
    String? profileImage,
    DateTime? createdAt,
    List<String>? favoriteBooks,
    List<String>? purchasedBooks,
    Map<String, dynamic>? readingProgress,
    bool? isPremium,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
      createdAt: createdAt ?? this.createdAt,
      favoriteBooks: favoriteBooks ?? this.favoriteBooks,
      purchasedBooks: purchasedBooks ?? this.purchasedBooks,
      readingProgress: readingProgress ?? this.readingProgress,
      isPremium: isPremium ?? this.isPremium,
    );
  }
}
