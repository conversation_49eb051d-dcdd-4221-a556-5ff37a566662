import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/book.dart';
import '../services/library_service.dart';

class PaymentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LibraryService _libraryService = LibraryService();

  // Process payment for a book
  Future<bool> processPayment({
    required String userId,
    required Book book,
    required String paymentMethod,
    Map<String, String>? cardDetails,
  }) async {
    try {
      // Create payment record
      final paymentId = await _createPaymentRecord(
        userId: userId,
        book: book,
        paymentMethod: paymentMethod,
      );

      // Simulate payment processing based on method
      bool paymentSuccess = false;
      
      switch (paymentMethod) {
        case 'credit_card':
          paymentSuccess = await _processCreditCardPayment(cardDetails!);
          break;
        case 'apple_pay':
          paymentSuccess = await _processApplePayPayment();
          break;
        case 'stc_pay':
          paymentSuccess = await _processSTCPayPayment();
          break;
        case 'mada':
          paymentSuccess = await _processMadaPayment();
          break;
        default:
          throw Exception('طريقة دفع غير مدعومة');
      }

      if (paymentSuccess) {
        // Update payment status
        await _updatePaymentStatus(paymentId, 'completed');
        
        // Add book to user's library
        await _libraryService.addBookToLibrary(userId, book.id);
        
        // Update book purchase count
        await _updateBookPurchaseCount(book.id);
        
        return true;
      } else {
        // Update payment status to failed
        await _updatePaymentStatus(paymentId, 'failed');
        return false;
      }
    } catch (e) {
      throw Exception('فشل في معالجة الدفع: ${e.toString()}');
    }
  }

  // Create payment record in Firestore
  Future<String> _createPaymentRecord({
    required String userId,
    required Book book,
    required String paymentMethod,
  }) async {
    final tax = book.price * 0.15; // 15% VAT
    final total = book.price + tax;

    final paymentData = {
      'userId': userId,
      'bookId': book.id,
      'bookTitle': book.title,
      'bookPrice': book.price,
      'tax': tax,
      'total': total,
      'paymentMethod': paymentMethod,
      'status': 'pending',
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };

    final docRef = await _firestore.collection('payments').add(paymentData);
    return docRef.id;
  }

  // Update payment status
  Future<void> _updatePaymentStatus(String paymentId, String status) async {
    await _firestore.collection('payments').doc(paymentId).update({
      'status': status,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  // Update book purchase count
  Future<void> _updateBookPurchaseCount(String bookId) async {
    await _firestore.collection('books').doc(bookId).update({
      'purchaseCount': FieldValue.increment(1),
    });
  }

  // Simulate credit card payment processing
  Future<bool> _processCreditCardPayment(Map<String, String> cardDetails) async {
    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 2));
    
    // Basic validation
    final cardNumber = cardDetails['cardNumber']?.replaceAll(' ', '') ?? '';
    final expiryDate = cardDetails['expiryDate'] ?? '';
    final cvv = cardDetails['cvv'] ?? '';
    final cardHolder = cardDetails['cardHolder'] ?? '';

    // Validate card number (basic Luhn algorithm check)
    if (!_isValidCardNumber(cardNumber)) {
      throw Exception('رقم البطاقة غير صحيح');
    }

    // Validate expiry date
    if (!_isValidExpiryDate(expiryDate)) {
      throw Exception('تاريخ انتهاء البطاقة غير صحيح');
    }

    // Validate CVV
    if (cvv.length != 3 || !RegExp(r'^\d{3}$').hasMatch(cvv)) {
      throw Exception('رمز CVV غير صحيح');
    }

    // Validate card holder name
    if (cardHolder.trim().isEmpty) {
      throw Exception('اسم حامل البطاقة مطلوب');
    }

    // Simulate payment gateway response
    // In a real app, this would be an actual API call to a payment processor
    return _simulatePaymentGatewayResponse();
  }

  // Simulate Apple Pay payment processing
  Future<bool> _processApplePayPayment() async {
    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 1));
    
    // In a real app, this would integrate with Apple Pay SDK
    return _simulatePaymentGatewayResponse();
  }

  // Simulate STC Pay payment processing
  Future<bool> _processSTCPayPayment() async {
    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 2));
    
    // In a real app, this would integrate with STC Pay API
    return _simulatePaymentGatewayResponse();
  }

  // Simulate Mada payment processing
  Future<bool> _processMadaPayment() async {
    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 2));
    
    // In a real app, this would integrate with Mada payment system
    return _simulatePaymentGatewayResponse();
  }

  // Simulate payment gateway response
  bool _simulatePaymentGatewayResponse() {
    // Simulate 95% success rate
    return DateTime.now().millisecond % 20 != 0;
  }

  // Validate card number using Luhn algorithm
  bool _isValidCardNumber(String cardNumber) {
    if (cardNumber.length != 16 || !RegExp(r'^\d{16}$').hasMatch(cardNumber)) {
      return false;
    }

    int sum = 0;
    bool alternate = false;

    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);

      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }

      sum += digit;
      alternate = !alternate;
    }

    return sum % 10 == 0;
  }

  // Validate expiry date
  bool _isValidExpiryDate(String expiryDate) {
    if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(expiryDate)) {
      return false;
    }

    final parts = expiryDate.split('/');
    final month = int.tryParse(parts[0]);
    final year = int.tryParse(parts[1]);

    if (month == null || year == null) {
      return false;
    }

    if (month < 1 || month > 12) {
      return false;
    }

    final now = DateTime.now();
    final currentYear = now.year % 100;
    final currentMonth = now.month;

    if (year < currentYear || (year == currentYear && month < currentMonth)) {
      return false;
    }

    return true;
  }

  // Get user's payment history
  Future<List<Map<String, dynamic>>> getPaymentHistory(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('payments')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      throw Exception('فشل في جلب سجل المدفوعات: ${e.toString()}');
    }
  }

  // Get payment details by ID
  Future<Map<String, dynamic>?> getPaymentDetails(String paymentId) async {
    try {
      final doc = await _firestore.collection('payments').doc(paymentId).get();
      if (doc.exists) {
        final data = doc.data()!;
        data['id'] = doc.id;
        return data;
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب تفاصيل الدفع: ${e.toString()}');
    }
  }

  // Refund a payment (for admin use)
  Future<bool> refundPayment(String paymentId, String reason) async {
    try {
      final paymentDoc = await _firestore.collection('payments').doc(paymentId).get();
      if (!paymentDoc.exists) {
        throw Exception('الدفعة غير موجودة');
      }

      final paymentData = paymentDoc.data()!;
      if (paymentData['status'] != 'completed') {
        throw Exception('لا يمكن استرداد دفعة غير مكتملة');
      }

      // Update payment status to refunded
      await _firestore.collection('payments').doc(paymentId).update({
        'status': 'refunded',
        'refundReason': reason,
        'refundedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Remove book from user's library
      await _libraryService.removeBookFromLibrary(
        paymentData['userId'],
        paymentData['bookId'],
      );

      return true;
    } catch (e) {
      throw Exception('فشل في استرداد الدفع: ${e.toString()}');
    }
  }

  // Get payment statistics (for admin use)
  Future<Map<String, dynamic>> getPaymentStatistics() async {
    try {
      final querySnapshot = await _firestore.collection('payments').get();
      
      double totalRevenue = 0;
      int totalTransactions = 0;
      int successfulTransactions = 0;
      int failedTransactions = 0;
      int refundedTransactions = 0;

      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        totalTransactions++;
        
        switch (data['status']) {
          case 'completed':
            successfulTransactions++;
            totalRevenue += (data['total'] ?? 0).toDouble();
            break;
          case 'failed':
            failedTransactions++;
            break;
          case 'refunded':
            refundedTransactions++;
            totalRevenue -= (data['total'] ?? 0).toDouble();
            break;
        }
      }

      return {
        'totalRevenue': totalRevenue,
        'totalTransactions': totalTransactions,
        'successfulTransactions': successfulTransactions,
        'failedTransactions': failedTransactions,
        'refundedTransactions': refundedTransactions,
        'successRate': totalTransactions > 0 
            ? (successfulTransactions / totalTransactions) * 100 
            : 0,
      };
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات المدفوعات: ${e.toString()}');
    }
  }
}
