import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import '../models/book.dart';

class DownloadService {
  static const String _downloadFolder = 'books';
  static const String _previewFolder = 'previews';

  // Download or get cached book file
  Future<String> downloadBook(Book book, {bool isPreview = false}) async {
    try {
      final fileName = _getFileName(book, isPreview);
      final localPath = await _getLocalFilePath(fileName, isPreview);
      
      // Check if file already exists
      final file = File(localPath);
      if (await file.exists()) {
        return localPath;
      }

      // Download the file
      final url = isPreview ? book.previewUrl : book.downloadUrl;
      if (url.isEmpty) {
        throw Exception(isPreview 
            ? 'رابط المعاينة غير متوفر' 
            : 'رابط التحميل غير متوفر');
      }

      await _downloadFile(url, localPath);
      return localPath;
    } catch (e) {
      throw Exception('فشل في تحميل الكتاب: ${e.toString()}');
    }
  }

  // Download file from URL
  Future<void> _downloadFile(String url, String localPath) async {
    final response = await http.get(Uri.parse(url));
    
    if (response.statusCode == 200) {
      final file = File(localPath);
      await file.create(recursive: true);
      await file.writeAsBytes(response.bodyBytes);
    } else {
      throw Exception('فشل في تحميل الملف: ${response.statusCode}');
    }
  }

  // Get local file path
  Future<String> _getLocalFilePath(String fileName, bool isPreview) async {
    final directory = await getApplicationDocumentsDirectory();
    final folder = isPreview ? _previewFolder : _downloadFolder;
    return '${directory.path}/$folder/$fileName';
  }

  // Generate file name
  String _getFileName(Book book, bool isPreview) {
    final extension = _getFileExtension(book.format);
    final prefix = isPreview ? 'preview_' : '';
    return '$prefix${book.id}.$extension';
  }

  // Get file extension based on format
  String _getFileExtension(BookFormat format) {
    switch (format) {
      case BookFormat.pdf:
        return 'pdf';
      case BookFormat.epub:
        return 'epub';
      case BookFormat.mobi:
        return 'mobi';
    }
  }

  // Check if book is downloaded
  Future<bool> isBookDownloaded(Book book, {bool isPreview = false}) async {
    try {
      final fileName = _getFileName(book, isPreview);
      final localPath = await _getLocalFilePath(fileName, isPreview);
      final file = File(localPath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  // Get downloaded book file size
  Future<int> getBookFileSize(Book book, {bool isPreview = false}) async {
    try {
      final fileName = _getFileName(book, isPreview);
      final localPath = await _getLocalFilePath(fileName, isPreview);
      final file = File(localPath);
      
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  // Delete downloaded book
  Future<bool> deleteDownloadedBook(Book book, {bool isPreview = false}) async {
    try {
      final fileName = _getFileName(book, isPreview);
      final localPath = await _getLocalFilePath(fileName, isPreview);
      final file = File(localPath);
      
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Get all downloaded books
  Future<List<String>> getDownloadedBooks({bool includePreview = false}) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final downloadDir = Directory('${directory.path}/$_downloadFolder');
      final previewDir = Directory('${directory.path}/$_previewFolder');
      
      final downloadedBooks = <String>[];
      
      // Get regular downloads
      if (await downloadDir.exists()) {
        final files = await downloadDir.list().toList();
        for (final file in files) {
          if (file is File) {
            final fileName = file.path.split('/').last;
            final bookId = fileName.split('.').first;
            downloadedBooks.add(bookId);
          }
        }
      }
      
      // Get previews if requested
      if (includePreview && await previewDir.exists()) {
        final files = await previewDir.list().toList();
        for (final file in files) {
          if (file is File) {
            final fileName = file.path.split('/').last;
            if (fileName.startsWith('preview_')) {
              final bookId = fileName.substring(8).split('.').first;
              downloadedBooks.add('preview_$bookId');
            }
          }
        }
      }
      
      return downloadedBooks;
    } catch (e) {
      return [];
    }
  }

  // Get total storage used by downloads
  Future<int> getTotalStorageUsed() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final downloadDir = Directory('${directory.path}/$_downloadFolder');
      final previewDir = Directory('${directory.path}/$_previewFolder');
      
      int totalSize = 0;
      
      // Calculate download folder size
      if (await downloadDir.exists()) {
        final files = await downloadDir.list(recursive: true).toList();
        for (final file in files) {
          if (file is File) {
            totalSize += await file.length();
          }
        }
      }
      
      // Calculate preview folder size
      if (await previewDir.exists()) {
        final files = await previewDir.list(recursive: true).toList();
        for (final file in files) {
          if (file is File) {
            totalSize += await file.length();
          }
        }
      }
      
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  // Clear all downloads
  Future<bool> clearAllDownloads({bool includePreview = false}) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final downloadDir = Directory('${directory.path}/$_downloadFolder');
      final previewDir = Directory('${directory.path}/$_previewFolder');
      
      // Clear download folder
      if (await downloadDir.exists()) {
        await downloadDir.delete(recursive: true);
      }
      
      // Clear preview folder if requested
      if (includePreview && await previewDir.exists()) {
        await previewDir.delete(recursive: true);
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Download book with progress callback
  Future<String> downloadBookWithProgress(
    Book book, {
    bool isPreview = false,
    Function(int received, int total)? onProgress,
  }) async {
    try {
      final fileName = _getFileName(book, isPreview);
      final localPath = await _getLocalFilePath(fileName, isPreview);
      
      // Check if file already exists
      final file = File(localPath);
      if (await file.exists()) {
        return localPath;
      }

      // Download the file with progress
      final url = isPreview ? book.previewUrl : book.downloadUrl;
      if (url.isEmpty) {
        throw Exception(isPreview 
            ? 'رابط المعاينة غير متوفر' 
            : 'رابط التحميل غير متوفر');
      }

      await _downloadFileWithProgress(url, localPath, onProgress);
      return localPath;
    } catch (e) {
      throw Exception('فشل في تحميل الكتاب: ${e.toString()}');
    }
  }

  // Download file with progress tracking
  Future<void> _downloadFileWithProgress(
    String url,
    String localPath,
    Function(int received, int total)? onProgress,
  ) async {
    final request = http.Request('GET', Uri.parse(url));
    final response = await request.send();
    
    if (response.statusCode == 200) {
      final file = File(localPath);
      await file.create(recursive: true);
      
      final sink = file.openWrite();
      int received = 0;
      final total = response.contentLength ?? 0;
      
      await response.stream.listen(
        (chunk) {
          sink.add(chunk);
          received += chunk.length;
          onProgress?.call(received, total);
        },
        onDone: () async {
          await sink.close();
        },
        onError: (error) async {
          await sink.close();
          throw error;
        },
      ).asFuture();
    } else {
      throw Exception('فشل في تحميل الملف: ${response.statusCode}');
    }
  }

  // Format file size for display
  String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // Get download progress for a book
  Future<double> getDownloadProgress(Book book, {bool isPreview = false}) async {
    try {
      final fileName = _getFileName(book, isPreview);
      final localPath = await _getLocalFilePath(fileName, isPreview);
      final file = File(localPath);
      
      if (await file.exists()) {
        return 1.0; // Fully downloaded
      }
      
      // Check if there's a partial download
      final tempPath = '$localPath.tmp';
      final tempFile = File(tempPath);
      
      if (await tempFile.exists()) {
        final currentSize = await tempFile.length();
        // You would need to store the expected file size somewhere
        // For now, return 0 if not complete
        return 0.0;
      }
      
      return 0.0; // Not downloaded
    } catch (e) {
      return 0.0;
    }
  }
}
