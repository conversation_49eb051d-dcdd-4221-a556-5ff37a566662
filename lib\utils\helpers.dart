import 'dart:io';
import 'dart:math';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'constants.dart';

/// Utility class for common helper functions
class Helpers {
  /// Format price with currency
  static String formatPrice(double price) {
    if (price == 0) {
      return 'مجاني';
    }
    return '${price.toStringAsFixed(2)} ${AppConstants.currencySymbol}';
  }

  /// Format date in Arabic
  static String formatDate(DateTime date) {
    final formatter = DateFormat('dd/MM/yyyy', 'ar');
    return formatter.format(date);
  }

  /// Format date with time in Arabic
  static String formatDateTime(DateTime date) {
    final formatter = DateFormat('dd/MM/yyyy HH:mm', 'ar');
    return formatter.format(date);
  }

  /// Format relative time (e.g., "منذ ساعتين")
  static String formatRelativeTime(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${_getDayUnit(difference.inDays)}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${_getHourUnit(difference.inHours)}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${_getMinuteUnit(difference.inMinutes)}';
    } else {
      return 'الآن';
    }
  }

  /// Format file size
  static String formatFileSize(int bytes) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB'];
    final i = (log(bytes) / log(1024)).floor();
    return '${(bytes / pow(1024, i)).toStringAsFixed(1)} ${suffixes[i]}';
  }

  /// Format reading time
  static String formatReadingTime(int minutes) {
    if (minutes < 60) {
      return '$minutes دقيقة';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours ${_getHourUnit(hours)}';
      } else {
        return '$hours ${_getHourUnit(hours)} و $remainingMinutes دقيقة';
      }
    }
  }

  /// Format rating with stars
  static String formatRating(double rating) {
    return '${rating.toStringAsFixed(1)} ⭐';
  }

  /// Get reading progress percentage
  static double getReadingProgress(int currentPage, int totalPages) {
    if (totalPages <= 0) return 0.0;
    return (currentPage / totalPages).clamp(0.0, 1.0);
  }

  /// Format reading progress
  static String formatReadingProgress(int currentPage, int totalPages) {
    final percentage =
        (getReadingProgress(currentPage, totalPages) * 100).round();
    return '$percentage% ($currentPage من $totalPages)';
  }

  /// Validate email
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate password
  static bool isValidPassword(String password) {
    return password.length >= AppConstants.minPasswordLength;
  }

  /// Generate random string
  static String generateRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
          length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// Show snackbar
  static void showSnackBar(BuildContext context, String message,
      {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.mediumRadius),
        ),
      ),
    );
  }

  /// Show loading dialog
  static void showLoadingDialog(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            if (message != null) ...[
              const SizedBox(height: UIConstants.mediumSpacing),
              Text(message),
            ],
          ],
        ),
      ),
    );
  }

  /// Hide loading dialog
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// Show confirmation dialog
  static Future<bool> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// Check if device is tablet
  static bool isTablet(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.shortestSide >= 600;
  }

  /// Get responsive value based on screen size
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final size = MediaQuery.of(context).size;
    if (size.width >= 1200 && desktop != null) {
      return desktop;
    } else if (size.width >= 600 && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// Truncate text with ellipsis
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  /// Get file extension
  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }

  /// Check if file exists
  static Future<bool> fileExists(String path) async {
    return await File(path).exists();
  }

  /// Get file size
  static Future<int> getFileSize(String path) async {
    final file = File(path);
    if (await file.exists()) {
      return await file.length();
    }
    return 0;
  }

  /// Convert hex color to Color
  static Color hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }

  /// Get contrast color (black or white) for given color
  static Color getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  /// Debounce function calls
  static Timer? _debounceTimer;
  static void debounce(VoidCallback callback, Duration delay) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  /// Format book category in Arabic
  static String formatBookCategory(String category) {
    switch (category.toLowerCase()) {
      case 'literature':
        return 'الأدب';
      case 'science':
        return 'العلوم';
      case 'novels':
        return 'الروايات';
      case 'history':
        return 'التاريخ';
      case 'religion':
        return 'الدين';
      case 'philosophy':
        return 'الفلسفة';
      case 'poetry':
        return 'الشعر';
      case 'children':
        return 'الأطفال';
      case 'biography':
        return 'السيرة الذاتية';
      case 'selfdevelopment':
        return 'تطوير الذات';
      default:
        return category;
    }
  }

  /// Format book format in Arabic
  static String formatBookFormat(String format) {
    switch (format.toLowerCase()) {
      case 'pdf':
        return 'PDF';
      case 'epub':
        return 'EPUB';
      case 'mobi':
        return 'MOBI';
      default:
        return format.toUpperCase();
    }
  }

  /// Calculate estimated reading time
  static int calculateReadingTime(int pageCount,
      {int wordsPerPage = 250, int wordsPerMinute = 200}) {
    final totalWords = pageCount * wordsPerPage;
    return (totalWords / wordsPerMinute).ceil();
  }

  /// Format download progress
  static String formatDownloadProgress(int downloaded, int total) {
    if (total <= 0) return '0%';
    final percentage = ((downloaded / total) * 100).round();
    return '$percentage% (${formatFileSize(downloaded)} / ${formatFileSize(total)})';
  }

  /// Generate book excerpt
  static String generateExcerpt(String text, {int maxLength = 150}) {
    if (text.length <= maxLength) return text;

    // Try to break at sentence end
    final sentences = text.split(RegExp(r'[.!?] +'));
    String excerpt = '';

    for (final sentence in sentences) {
      if ('$excerpt$sentence'.length > maxLength) break;
      excerpt = '$excerpt$sentence. ';
    }

    if (excerpt.isEmpty) {
      excerpt = text.substring(0, maxLength);
    }

    return '${excerpt.trim()}...';
  }

  /// Validate Arabic text
  static bool isArabicText(String text) {
    final arabicRegex = RegExp(r'[؀-ۿ]');
    return arabicRegex.hasMatch(text);
  }

  /// Format search query for better results
  static String formatSearchQuery(String query) {
    return query
        .trim()
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s؀-ۿ]'), '')
        .replaceAll(RegExp(r'\s+'), ' ');
  }

  /// Generate star rating widget data
  static List<bool> generateStarRating(double rating) {
    final stars = <bool>[];
    final fullStars = rating.floor();
    final hasHalfStar = (rating - fullStars) >= 0.5;

    for (int i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.add(true);
      } else if (i == fullStars && hasHalfStar) {
        stars.add(true); // Half star treated as full for simplicity
      } else {
        stars.add(false);
      }
    }

    return stars;
  }

  /// Format review count in Arabic
  static String formatReviewCount(int count) {
    if (count == 0) return 'لا توجد مراجعات';
    if (count == 1) return 'مراجعة واحدة';
    if (count == 2) return 'مراجعتان';
    if (count <= 10) return '$count مراجعات';
    return '$count مراجعة';
  }

  /// Helper functions for Arabic pluralization
  static String _getDayUnit(int count) {
    if (count == 1) return 'يوم';
    if (count == 2) return 'يومين';
    if (count <= 10) return 'أيام';
    return 'يوماً';
  }

  static String _getHourUnit(int count) {
    if (count == 1) return 'ساعة';
    if (count == 2) return 'ساعتين';
    if (count <= 10) return 'ساعات';
    return 'ساعة';
  }

  static String _getMinuteUnit(int count) {
    if (count == 1) return 'دقيقة';
    if (count == 2) return 'دقيقتين';
    if (count <= 10) return 'دقائق';
    return 'دقيقة';
  }
}

/// Network and connectivity helpers
class NetworkHelpers {
  /// Check if URL is valid
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Extract domain from URL
  static String? extractDomain(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return null;
    }
  }

  /// Build query parameters
  static String buildQueryString(Map<String, dynamic> params) {
    final query = params.entries
        .where((entry) => entry.value != null)
        .map((entry) =>
            '${Uri.encodeComponent(entry.key)}=${Uri.encodeComponent(entry.value.toString())}')
        .join('&');
    return query.isNotEmpty ? '?$query' : '';
  }
}

/// Storage and cache helpers
class StorageHelpers {
  /// Generate cache key
  static String generateCacheKey(String prefix, Map<String, dynamic> params) {
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );
    final paramString =
        sortedParams.entries.map((e) => '${e.key}=${e.value}').join('&');
    return '${prefix}_${paramString.hashCode}';
  }

  /// Check if cache is expired
  static bool isCacheExpired(DateTime cacheTime, Duration maxAge) {
    return DateTime.now().difference(cacheTime) > maxAge;
  }

  /// Calculate cache size
  static Future<int> calculateDirectorySize(String directoryPath) async {
    final directory = Directory(directoryPath);
    if (!await directory.exists()) return 0;

    int totalSize = 0;
    await for (final entity in directory.list(recursive: true)) {
      if (entity is File) {
        totalSize += await entity.length();
      }
    }
    return totalSize;
  }
}

/// Reading and book helpers
class ReadingHelpers {
  /// Calculate reading speed (words per minute)
  static double calculateReadingSpeed(int wordsRead, Duration timeSpent) {
    if (timeSpent.inMinutes == 0) return 0.0;
    return wordsRead / timeSpent.inMinutes;
  }

  /// Estimate words in text
  static int estimateWordCount(String text) {
    if (text.isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).length;
  }

  /// Calculate reading streak
  static int calculateReadingStreak(List<DateTime> readingDates) {
    if (readingDates.isEmpty) return 0;

    readingDates.sort((a, b) => b.compareTo(a)); // Sort descending

    int streak = 0;
    DateTime currentDate = DateTime.now();

    for (final date in readingDates) {
      final daysDifference = currentDate.difference(date).inDays;

      if (daysDifference <= 1) {
        streak++;
        currentDate = date;
      } else {
        break;
      }
    }

    return streak;
  }

  /// Generate reading goals suggestions
  static List<int> generateReadingGoals(int currentBooksRead) {
    final baseGoals = [5, 10, 15, 20, 25, 30, 50];
    return baseGoals.where((goal) => goal > currentBooksRead).toList();
  }
}

/// UI and theme helpers
class UIHelpers {
  /// Check if text should be displayed RTL
  static bool shouldUseRTL(String text) {
    return Helpers.isArabicText(text);
  }

  /// Generate gradient colors
  static List<Color> generateGradientColors(Color baseColor) {
    final hsl = HSLColor.fromColor(baseColor);
    return [
      hsl.withLightness((hsl.lightness + 0.1).clamp(0.0, 1.0)).toColor(),
      baseColor,
      hsl.withLightness((hsl.lightness - 0.1).clamp(0.0, 1.0)).toColor(),
    ];
  }

  /// Calculate optimal text color for background
  static Color getOptimalTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// Generate book cover placeholder color
  static Color generateBookCoverColor(String bookId) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.red,
      Colors.amber,
    ];

    final index = bookId.hashCode % colors.length;
    return colors[index.abs()];
  }
}

/// Extension methods for common operations
extension StringExtensions on String {
  /// Check if string is empty or null
  bool get isNullOrEmpty => isEmpty;

  /// Capitalize first letter
  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }

  /// Remove extra whitespace
  String get trimmed => trim().replaceAll(RegExp(r'\s+'), ' ');

  /// Check if string is a valid email
  bool get isValidEmail => Helpers.isValidEmail(this);

  /// Check if string contains Arabic text
  bool get isArabic => Helpers.isArabicText(this);

  /// Format as search query
  String get asSearchQuery => Helpers.formatSearchQuery(this);

  /// Generate excerpt
  String excerpt({int maxLength = 150}) =>
      Helpers.generateExcerpt(this, maxLength: maxLength);
}

extension DateTimeExtensions on DateTime {
  /// Check if date is today
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  /// Check if date is yesterday
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year &&
        month == yesterday.month &&
        day == yesterday.day;
  }

  /// Get start of day
  DateTime get startOfDay => DateTime(year, month, day);

  /// Get end of day
  DateTime get endOfDay => DateTime(year, month, day, 23, 59, 59, 999);
}

extension ListExtensions<T> on List<T> {
  /// Get random element
  T get random => this[Random().nextInt(length)];

  /// Check if list is null or empty
  bool get isNullOrEmpty => isEmpty;

  /// Get unique elements
  List<T> get unique => toSet().toList();
}

/// Debounce utility class
class DebounceHelper {
  static Timer? _timer;

  /// Cancel current debounce timer
  static void cancel() {
    _timer?.cancel();
    _timer = null;
  }

  /// Execute callback after delay, canceling previous calls
  static void execute(VoidCallback callback, Duration delay) {
    cancel();
    _timer = Timer(delay, callback);
  }
}
