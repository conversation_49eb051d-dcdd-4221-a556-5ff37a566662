import 'package:flutter/material.dart';

/// App Constants
class AppConstants {
  // App Info
  static const String appName = 'إقرأ';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق قراءة الكتب الإلكترونية العربية';

  // API URLs
  static const String baseUrl = 'https://api.iqraa.app';
  static const String booksEndpoint = '/books';
  static const String authEndpoint = '/auth';
  static const String usersEndpoint = '/users';
  static const String paymentsEndpoint = '/payments';

  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String readingProgressKey = 'reading_progress';
  static const String downloadedBooksKey = 'downloaded_books';

  // File Paths
  static const String booksDirectory = 'books';
  static const String coversDirectory = 'covers';
  static const String tempDirectory = 'temp';

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Timeouts
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration splashDuration = Duration(seconds: 3);
  static const Duration animationDuration = Duration(milliseconds: 300);

  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int maxNameLength = 100;
  static const int maxBioLength = 500;

  // Reading Settings
  static const double minFontSize = 12.0;
  static const double maxFontSize = 32.0;
  static const double defaultFontSize = 16.0;
  static const double minLineHeight = 1.0;
  static const double maxLineHeight = 3.0;
  static const double defaultLineHeight = 1.5;

  // Payment
  static const String currency = 'SAR';
  static const String currencySymbol = 'ر.س';

  // Social
  static const int maxReviewLength = 1000;
  static const int minReviewLength = 10;
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
}

/// UI Constants
class UIConstants {
  // Spacing
  static const double smallSpacing = 8.0;
  static const double mediumSpacing = 16.0;
  static const double largeSpacing = 24.0;
  static const double extraLargeSpacing = 32.0;

  // Border Radius
  static const double smallRadius = 4.0;
  static const double mediumRadius = 8.0;
  static const double largeRadius = 16.0;
  static const double circularRadius = 50.0;

  // Elevation
  static const double lowElevation = 2.0;
  static const double mediumElevation = 4.0;
  static const double highElevation = 8.0;

  // Icon Sizes
  static const double smallIconSize = 16.0;
  static const double mediumIconSize = 24.0;
  static const double largeIconSize = 32.0;
  static const double extraLargeIconSize = 48.0;

  // Button Heights
  static const double buttonHeight = 48.0;
  static const double smallButtonHeight = 36.0;
  static const double largeButtonHeight = 56.0;

  // Card Dimensions
  static const double bookCardWidth = 120.0;
  static const double bookCardHeight = 180.0;
  static const double bookCoverAspectRatio = 2 / 3;

  // App Bar
  static const double appBarHeight = 56.0;
  static const double expandedAppBarHeight = 200.0;

  // Bottom Navigation
  static const double bottomNavHeight = 60.0;
}

/// Color Constants
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF00695C);
  static const Color primaryLight = Color(0xFF439889);
  static const Color primaryDark = Color(0xFF003D33);

  // Secondary Colors
  static const Color secondary = Color(0xFFFF6F00);
  static const Color secondaryLight = Color(0xFFFF9F40);
  static const Color secondaryDark = Color(0xFFC43E00);

  // Background Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color cardBackground = Color(0xFFFFFFFF);

  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Rating Colors
  static const Color ratingFilled = Color(0xFFFFD700);
  static const Color ratingEmpty = Color(0xFFE0E0E0);

  // Reading Colors
  static const Color readingBackground = Color(0xFFFFFBF0);
  static const Color readingText = Color(0xFF2E2E2E);
  static const Color highlight = Color(0xFFFFEB3B);

  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFBDBDBD);

  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color shimmer = Color(0xFFE0E0E0);
}

/// Text Styles
class AppTextStyles {
  // Headlines
  static const TextStyle headline1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static const TextStyle headline2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static const TextStyle headline3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );

  // Body Text
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
  );

  // Captions
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textHint,
  );

  // Buttons
  static const TextStyle button = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: AppColors.textOnPrimary,
  );

  // Reading Text
  static const TextStyle reading = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.readingText,
    height: 1.5,
  );
}

/// Animation Constants
class AnimationConstants {
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration verySlow = Duration(milliseconds: 1000);

  static const Curve easeIn = Curves.easeIn;
  static const Curve easeOut = Curves.easeOut;
  static const Curve easeInOut = Curves.easeInOut;
  static const Curve bounce = Curves.bounceOut;
}

/// Error Messages
class ErrorMessages {
  static const String networkError = 'خطأ في الاتصال بالإنترنت';
  static const String serverError = 'خطأ في الخادم';
  static const String unknownError = 'حدث خطأ غير متوقع';
  static const String invalidCredentials = 'بيانات الدخول غير صحيحة';
  static const String emailAlreadyExists = 'البريد الإلكتروني مستخدم بالفعل';
  static const String weakPassword = 'كلمة المرور ضعيفة';
  static const String invalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String userNotFound = 'المستخدم غير موجود';
  static const String bookNotFound = 'الكتاب غير موجود';
  static const String downloadFailed = 'فشل في تحميل الكتاب';
  static const String paymentFailed = 'فشل في عملية الدفع';
  static const String insufficientStorage = 'مساحة التخزين غير كافية';
}

/// Success Messages
class SuccessMessages {
  static const String loginSuccess = 'تم تسجيل الدخول بنجاح';
  static const String registerSuccess = 'تم إنشاء الحساب بنجاح';
  static const String logoutSuccess = 'تم تسجيل الخروج بنجاح';
  static const String profileUpdated = 'تم تحديث الملف الشخصي';
  static const String bookDownloaded = 'تم تحميل الكتاب بنجاح';
  static const String bookPurchased = 'تم شراء الكتاب بنجاح';
  static const String reviewSubmitted = 'تم إرسال المراجعة بنجاح';
  static const String passwordChanged = 'تم تغيير كلمة المرور';
  static const String emailVerified = 'تم تأكيد البريد الإلكتروني';
}
