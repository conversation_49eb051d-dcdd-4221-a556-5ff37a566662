import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'test_helpers.dart';

void main() {
  group('Test Helpers Tests', () {
    testWidgets('createTestApp should create app with providers',
        (WidgetTester tester) async {
      final testWidget = const Text('Test Widget');

      final app = createTestApp(child: testWidget);

      await tester.pumpWidget(app);

      expect(find.text('Test Widget'), findsOneWidget);
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('pumpWidgetWithProviders should work correctly',
        (WidgetTester tester) async {
      final testWidget = const Scaffold(
        body: Text('Test Content'),
      );

      await pumpWidgetWithProviders(tester, testWidget);

      expect(find.text('Test Content'), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
    });

    test('TestData should contain correct test values', () {
      expect(TestData.testEmail, '<EMAIL>');
      expect(TestData.testPassword, 'password123');
      expect(TestData.testUserName, 'Test User');
      expect(TestData.testBookTitle, 'كتاب تجريبي');
      expect(TestData.testAuthor, 'مؤلف تجريبي');
    });

    testWidgets('TestUtils.waitForAnimations should work',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 100,
              height: 100,
              color: Colors.blue,
            ),
          ),
        ),
      );

      // Should complete without throwing
      await TestUtils.waitForAnimations(tester);
      expect(find.byType(AnimatedContainer), findsOneWidget);
    });

    testWidgets('TestUtils.enterText should work with TextField',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TextField(
              key: const Key('test_field'),
            ),
          ),
        ),
      );

      await TestUtils.enterText(tester, 'Test Input', fieldKey: 'test_field');

      expect(find.text('Test Input'), findsOneWidget);
    });

    testWidgets('TestUtils.tapButton should work with different button types',
        (WidgetTester tester) async {
      bool buttonPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                ElevatedButton(
                  onPressed: () => buttonPressed = true,
                  child: const Text('Test Button'),
                ),
                ElevatedButton(
                  key: const Key('key_button'),
                  onPressed: () => buttonPressed = true,
                  child: const Text('Key Button'),
                ),
              ],
            ),
          ),
        ),
      );

      // Test tap by text
      await TestUtils.tapButton(tester, buttonText: 'Test Button');
      expect(buttonPressed, true);

      buttonPressed = false;

      // Test tap by key
      await TestUtils.tapButton(tester, buttonKey: 'key_button');
      expect(buttonPressed, true);
    });

    testWidgets('TestUtils expectation methods should work',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                Text('Error: Something went wrong'),
                Text('Success: Operation completed'),
                CircularProgressIndicator(),
              ],
            ),
          ),
        ),
      );

      // Test expectation methods
      TestUtils.expectErrorMessage('Error: Something went wrong');
      TestUtils.expectSuccessMessage('Success: Operation completed');
      TestUtils.expectLoadingIndicator();
      TestUtils.expectScreen<Scaffold>();
    });

    test('CustomMatchers should provide correct matchers', () {
      // Test that matchers are created correctly
      final textMatcher = CustomMatchers.hasText('test');
      final iconMatcher = CustomMatchers.hasIcon(Icons.home);
      final widgetMatcher = CustomMatchers.hasWidget<Container>();

      expect(textMatcher, isA<Matcher>());
      expect(iconMatcher, isA<Matcher>());
      expect(widgetMatcher, isA<Matcher>());
    });

    test('TestModelFactory should create test models', () {
      // Test book creation
      final book = TestModelFactory.createTestBook();
      expect(book.id, TestData.testBookId);
      expect(book.title, TestData.testBookTitle);
      expect(book.author, TestData.testAuthor);
      expect(book.price, TestData.testBookPrice);

      // Test user creation
      final user = TestModelFactory.createTestUser();
      expect(user.id, TestData.testUserId);
      expect(user.email, TestData.testEmail);
      expect(user.name, TestData.testUserName);
      expect(user.isPremium, false);

      // Test book list creation
      final bookList = TestModelFactory.createTestBookList(3);
      expect(bookList.length, 3);
      expect(bookList[0].id, 'book-0');
      expect(bookList[1].id, 'book-1');

      // Test free book
      final freeBook = TestModelFactory.createFreeBook();
      expect(freeBook.isFree, true);
      expect(freeBook.price, 0.0);

      // Test popular book
      final popularBook = TestModelFactory.createPopularBook();
      expect(popularBook.isPopular, true);
      expect(popularBook.rating, 4.8);
    });
  });
}
