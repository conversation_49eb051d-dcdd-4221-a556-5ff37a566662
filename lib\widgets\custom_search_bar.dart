import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/book_provider.dart';
import '../screens/books/search_results_screen.dart';

class CustomSearchBar extends StatefulWidget {
  const CustomSearchBar({super.key});

  @override
  State<CustomSearchBar> createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => SearchResultsScreen(query: query),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        textInputAction: TextInputAction.search,
        onSubmitted: (_) => _performSearch(),
        decoration: InputDecoration(
          hintText: 'ابحث عن كتاب، مؤلف، أو موضوع...',
          hintStyle: TextStyle(
            color: Colors.grey[500],
            fontSize: 14,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: Colors.grey[500],
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: Colors.grey[500],
                  ),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {});
                  },
                )
              : IconButton(
                  icon: Icon(
                    Icons.mic,
                    color: Colors.grey[500],
                  ),
                  onPressed: () {
                    // TODO: Implement voice search
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('البحث الصوتي قريباً...'),
                      ),
                    );
                  },
                ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onChanged: (value) {
          setState(() {});
        },
      ),
    );
  }
}

class SearchSuggestions extends StatelessWidget {
  final String query;
  final VoidCallback onClose;

  const SearchSuggestions({
    super.key,
    required this.query,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'اقتراحات البحث',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onClose,
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Suggestions List
          ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildSuggestionItem(
                context,
                icon: Icons.history,
                text: 'البحث الأخير: "$query"',
                onTap: () {
                  // TODO: Implement recent search
                },
              ),
              _buildSuggestionItem(
                context,
                icon: Icons.trending_up,
                text: 'الأكثر بحثاً: الأسود يليق بك',
                onTap: () {
                  // TODO: Implement trending search
                },
              ),
              _buildSuggestionItem(
                context,
                icon: Icons.category,
                text: 'تصنيف: الروايات',
                onTap: () {
                  // TODO: Navigate to category
                },
              ),
              _buildSuggestionItem(
                context,
                icon: Icons.person,
                text: 'مؤلف: أحلام مستغانمي',
                onTap: () {
                  // TODO: Search by author
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(
    BuildContext context, {
    required IconData icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Colors.grey[600],
        size: 20,
      ),
      title: Text(
        text,
        style: const TextStyle(fontSize: 14),
      ),
      onTap: onTap,
      dense: true,
    );
  }
}
