import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user.dart' as app_user;

class AuthService {
  final firebase_auth.FirebaseAuth _auth = firebase_auth.FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  firebase_auth.User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<firebase_auth.User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with email and password
  Future<app_user.User?> signInWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        return await getUserData(credential.user!.uid);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في تسجيل الدخول: ${e.toString()}');
    }
  }

  // Register with email and password
  Future<app_user.User?> registerWithEmailAndPassword(
    String email, 
    String password, 
    String name
  ) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        // Create user document in Firestore
        final user = app_user.User(
          id: credential.user!.uid,
          email: email,
          name: name,
          createdAt: DateTime.now(),
        );
        
        await _firestore.collection('users').doc(user.id).set(user.toJson());
        return user;
      }
      return null;
    } catch (e) {
      throw Exception('فشل في إنشاء الحساب: ${e.toString()}');
    }
  }

  // Get user data from Firestore
  Future<app_user.User?> getUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return app_user.User.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب بيانات المستخدم: ${e.toString()}');
    }
  }

  // Update user data
  Future<void> updateUserData(app_user.User user) async {
    try {
      await _firestore.collection('users').doc(user.id).update(user.toJson());
    } catch (e) {
      throw Exception('فشل في تحديث بيانات المستخدم: ${e.toString()}');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw Exception('فشل في تسجيل الخروج: ${e.toString()}');
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception('فشل في إرسال رابط إعادة تعيين كلمة المرور: ${e.toString()}');
    }
  }

  // Add book to favorites
  Future<void> addToFavorites(String bookId) async {
    if (currentUser == null) return;
    
    try {
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'favoriteBooks': FieldValue.arrayUnion([bookId])
      });
    } catch (e) {
      throw Exception('فشل في إضافة الكتاب للمفضلة: ${e.toString()}');
    }
  }

  // Remove book from favorites
  Future<void> removeFromFavorites(String bookId) async {
    if (currentUser == null) return;
    
    try {
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'favoriteBooks': FieldValue.arrayRemove([bookId])
      });
    } catch (e) {
      throw Exception('فشل في إزالة الكتاب من المفضلة: ${e.toString()}');
    }
  }

  // Add purchased book
  Future<void> addPurchasedBook(String bookId) async {
    if (currentUser == null) return;
    
    try {
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'purchasedBooks': FieldValue.arrayUnion([bookId])
      });
    } catch (e) {
      throw Exception('فشل في إضافة الكتاب للمشتريات: ${e.toString()}');
    }
  }

  // Update reading progress
  Future<void> updateReadingProgress(String bookId, Map<String, dynamic> progress) async {
    if (currentUser == null) return;
    
    try {
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'readingProgress.$bookId': progress
      });
    } catch (e) {
      throw Exception('فشل في تحديث تقدم القراءة: ${e.toString()}');
    }
  }
}
