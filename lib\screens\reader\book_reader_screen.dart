import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:epub_view/epub_view.dart';
import 'package:provider/provider.dart';
import '../../models/book.dart';
import '../../providers/auth_provider.dart';
import '../../services/download_service.dart';
import 'dart:io';

class BookReaderScreen extends StatefulWidget {
  final Book book;
  final bool isPreview;

  const BookReaderScreen({
    super.key,
    required this.book,
    this.isPreview = false,
  });

  @override
  State<BookReaderScreen> createState() => _BookReaderScreenState();
}

class _BookReaderScreenState extends State<BookReaderScreen> {
  final DownloadService _downloadService = DownloadService();
  
  bool _isLoading = true;
  bool _showControls = true;
  String? _localFilePath;
  String? _error;
  
  // PDF specific
  PDFViewController? _pdfController;
  int _currentPage = 0;
  int _totalPages = 0;
  
  // EPUB specific
  EpubController? _epubController;
  
  // Reading settings
  double _fontSize = 16.0;
  Color _backgroundColor = Colors.white;
  Color _textColor = Colors.black;
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _loadBook();
    _loadReadingProgress();
  }

  @override
  void dispose() {
    _epubController?.dispose();
    super.dispose();
  }

  Future<void> _loadBook() async {
    try {
      setState(() => _isLoading = true);
      
      // Download or get cached book file
      final filePath = await _downloadService.downloadBook(
        widget.book,
        isPreview: widget.isPreview,
      );
      
      setState(() {
        _localFilePath = filePath;
        _isLoading = false;
      });
      
      // Initialize EPUB controller if needed
      if (widget.book.format == BookFormat.epub && _localFilePath != null) {
        _epubController = EpubController.file(File(_localFilePath!));
      }
      
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadReadingProgress() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.user;
    
    if (user != null && !widget.isPreview) {
      final progress = user.readingProgress[widget.book.id];
      if (progress != null) {
        setState(() {
          _currentPage = progress['currentPage'] ?? 0;
        });
      }
    }
  }

  Future<void> _saveReadingProgress() async {
    if (widget.isPreview) return;
    
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.user;
    
    if (user != null) {
      // TODO: Save reading progress to Firebase
      // This would update the user's readingProgress map
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _backgroundColor,
      appBar: _showControls ? _buildAppBar() : null,
      body: _buildBody(),
      bottomNavigationBar: _showControls ? _buildBottomControls() : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: _backgroundColor,
      foregroundColor: _textColor,
      elevation: 0,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.book.title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (widget.isPreview)
            Text(
              'معاينة',
              style: TextStyle(
                fontSize: 12,
                color: _textColor.withOpacity(0.7),
              ),
            ),
        ],
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.bookmark_border, color: _textColor),
          onPressed: _addBookmark,
        ),
        IconButton(
          icon: Icon(Icons.settings, color: _textColor),
          onPressed: _showReaderSettings,
        ),
      ],
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'جاري تحميل الكتاب...',
              style: TextStyle(color: _textColor),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل الكتاب',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(color: _textColor.withOpacity(0.7)),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadBook,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_localFilePath == null) {
      return Center(
        child: Text(
          'لم يتم العثور على الكتاب',
          style: TextStyle(color: _textColor),
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        setState(() => _showControls = !_showControls);
      },
      child: _buildReader(),
    );
  }

  Widget _buildReader() {
    switch (widget.book.format) {
      case BookFormat.pdf:
        return _buildPDFReader();
      case BookFormat.epub:
        return _buildEPUBReader();
      case BookFormat.mobi:
        return _buildMOBIReader();
    }
  }

  Widget _buildPDFReader() {
    return PDFView(
      filePath: _localFilePath!,
      enableSwipe: true,
      swipeHorizontal: false,
      autoSpacing: false,
      pageFling: true,
      pageSnap: true,
      defaultPage: _currentPage,
      fitPolicy: FitPolicy.BOTH,
      preventLinkNavigation: false,
      onRender: (pages) {
        setState(() => _totalPages = pages ?? 0);
      },
      onViewCreated: (PDFViewController controller) {
        _pdfController = controller;
      },
      onPageChanged: (page, total) {
        setState(() {
          _currentPage = page ?? 0;
          _totalPages = total ?? 0;
        });
        _saveReadingProgress();
      },
      onError: (error) {
        setState(() => _error = error.toString());
      },
    );
  }

  Widget _buildEPUBReader() {
    if (_epubController == null) {
      return Center(
        child: Text(
          'خطأ في تحميل الكتاب الإلكتروني',
          style: TextStyle(color: _textColor),
        ),
      );
    }

    return EpubView(
      controller: _epubController!,
      onExternalLinkPressed: (href) {
        // Handle external links
      },
      onChapterChanged: (chapter) {
        // Save reading progress
        _saveReadingProgress();
      },
      builders: EpubViewBuilders<DefaultBuilderOptions>(
        options: const DefaultBuilderOptions(
          textStyle: TextStyle(
            height: 1.6,
            fontSize: 16,
          ),
        ),
        chapterDividerBuilder: (_) => const Divider(),
      ),
    );
  }

  Widget _buildMOBIReader() {
    // MOBI format is not directly supported, show message
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 64,
            color: _textColor.withOpacity(0.7),
          ),
          const SizedBox(height: 16),
          Text(
            'تنسيق MOBI غير مدعوم حالياً',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'يرجى تحويل الكتاب إلى تنسيق PDF أو EPUB',
            style: TextStyle(color: _textColor.withOpacity(0.7)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      color: _backgroundColor,
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress bar
          if (widget.book.format == BookFormat.pdf && _totalPages > 0)
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'صفحة ${_currentPage + 1}',
                      style: TextStyle(color: _textColor),
                    ),
                    Text(
                      'من $_totalPages',
                      style: TextStyle(color: _textColor),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: _totalPages > 0 ? (_currentPage + 1) / _totalPages : 0,
                  backgroundColor: _textColor.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          
          // Navigation controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: Icon(Icons.skip_previous, color: _textColor),
                onPressed: _goToPreviousPage,
              ),
              IconButton(
                icon: Icon(Icons.text_fields, color: _textColor),
                onPressed: _showReaderSettings,
              ),
              IconButton(
                icon: Icon(Icons.bookmark, color: _textColor),
                onPressed: _addBookmark,
              ),
              IconButton(
                icon: Icon(Icons.share, color: _textColor),
                onPressed: _shareProgress,
              ),
              IconButton(
                icon: Icon(Icons.skip_next, color: _textColor),
                onPressed: _goToNextPage,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _goToPreviousPage() {
    if (widget.book.format == BookFormat.pdf && _pdfController != null) {
      if (_currentPage > 0) {
        _pdfController!.setPage(_currentPage - 1);
      }
    } else if (widget.book.format == BookFormat.epub && _epubController != null) {
      _epubController!.previousPage();
    }
  }

  void _goToNextPage() {
    if (widget.book.format == BookFormat.pdf && _pdfController != null) {
      if (_currentPage < _totalPages - 1) {
        _pdfController!.setPage(_currentPage + 1);
      }
    } else if (widget.book.format == BookFormat.epub && _epubController != null) {
      _epubController!.nextPage();
    }
  }

  void _addBookmark() {
    // TODO: Implement bookmark functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إضافة إشارة مرجعية')),
    );
  }

  void _shareProgress() {
    // TODO: Implement share reading progress
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة المشاركة قيد التطوير')),
    );
  }

  void _showReaderSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: _backgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات القراءة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _textColor,
              ),
            ),
            const SizedBox(height: 20),
            
            // Font size
            Text('حجم الخط', style: TextStyle(color: _textColor)),
            Slider(
              value: _fontSize,
              min: 12.0,
              max: 24.0,
              divisions: 6,
              label: _fontSize.round().toString(),
              onChanged: (value) {
                setState(() => _fontSize = value);
              },
            ),
            
            const SizedBox(height: 16),
            
            // Theme toggle
            SwitchListTile(
              title: Text('الوضع الليلي', style: TextStyle(color: _textColor)),
              value: _isDarkMode,
              onChanged: (value) {
                setState(() {
                  _isDarkMode = value;
                  _backgroundColor = value ? Colors.black : Colors.white;
                  _textColor = value ? Colors.white : Colors.black;
                });
              },
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
