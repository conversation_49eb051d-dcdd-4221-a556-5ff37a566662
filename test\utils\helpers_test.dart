import 'package:flutter_test/flutter_test.dart';
import 'package:iqraa/utils/helpers.dart';

void main() {
  group('Helpers Tests', () {
    group('Price Formatting', () {
      test('should format price correctly', () {
        expect(Helpers.formatPrice(0), 'مجاني');
        expect(Helpers.formatPrice(25.99), '25.99 ر.س');
        expect(Helpers.formatPrice(100), '100.00 ر.س');
      });
    });

    group('Date Formatting', () {
      test('should format date correctly', () {
        final date = DateTime(2024, 1, 15);
        // Just test that it returns a non-empty string
        final formatted = Helpers.formatDate(date);
        expect(formatted, isNotEmpty);
        expect(formatted, isA<String>());
      });

      test('should format relative time correctly', () {
        final now = DateTime.now();
        final oneHourAgo = now.subtract(const Duration(hours: 1));
        final oneDayAgo = now.subtract(const Duration(days: 1));

        expect(Helpers.formatRelativeTime(oneHourAgo), contains('ساعة'));
        expect(Helpers.formatRelativeTime(oneDayAgo), contains('يوم'));
      });
    });

    group('File Size Formatting', () {
      test('should format file size correctly', () {
        expect(Helpers.formatFileSize(0), '0 B');
        expect(Helpers.formatFileSize(1024), '1.0 KB');
        expect(Helpers.formatFileSize(1024 * 1024), '1.0 MB');
        expect(Helpers.formatFileSize(1024 * 1024 * 1024), '1.0 GB');
      });
    });

    group('Reading Time Formatting', () {
      test('should format reading time correctly', () {
        expect(Helpers.formatReadingTime(30), '30 دقيقة');
        expect(Helpers.formatReadingTime(60), '1 ساعة');
        expect(Helpers.formatReadingTime(90), contains('ساعة'));
        expect(Helpers.formatReadingTime(90), contains('دقيقة'));
      });
    });

    group('Rating Formatting', () {
      test('should format rating correctly', () {
        expect(Helpers.formatRating(4.5), '4.5 ⭐');
        expect(Helpers.formatRating(3.0), '3.0 ⭐');
      });
    });

    group('Reading Progress', () {
      test('should calculate reading progress correctly', () {
        expect(Helpers.getReadingProgress(50, 100), 0.5);
        expect(Helpers.getReadingProgress(0, 100), 0.0);
        expect(Helpers.getReadingProgress(100, 100), 1.0);
        expect(Helpers.getReadingProgress(150, 100), 1.0); // Clamped
      });

      test('should format reading progress correctly', () {
        final progress = Helpers.formatReadingProgress(50, 100);
        expect(progress, contains('50%'));
        expect(progress, contains('50 من 100'));
      });
    });

    group('Validation', () {
      test('should validate email correctly', () {
        expect(Helpers.isValidEmail('<EMAIL>'), true);
        expect(Helpers.isValidEmail('invalid-email'), false);
        expect(Helpers.isValidEmail(''), false);
      });

      test('should validate password correctly', () {
        expect(Helpers.isValidPassword('123456'), true);
        expect(Helpers.isValidPassword('12345'), false);
        expect(Helpers.isValidPassword(''), false);
      });
    });

    group('Book Category Formatting', () {
      test('should format book categories in Arabic', () {
        expect(Helpers.formatBookCategory('literature'), 'الأدب');
        expect(Helpers.formatBookCategory('science'), 'العلوم');
        expect(Helpers.formatBookCategory('novels'), 'الروايات');
        expect(Helpers.formatBookCategory('unknown'), 'unknown');
      });
    });

    group('Book Format Formatting', () {
      test('should format book formats correctly', () {
        expect(Helpers.formatBookFormat('pdf'), 'PDF');
        expect(Helpers.formatBookFormat('epub'), 'EPUB');
        expect(Helpers.formatBookFormat('mobi'), 'MOBI');
        expect(Helpers.formatBookFormat('unknown'), 'UNKNOWN');
      });
    });

    group('Reading Time Calculation', () {
      test('should calculate reading time correctly', () {
        final readingTime = Helpers.calculateReadingTime(100);
        expect(readingTime, greaterThan(0));

        final customReadingTime = Helpers.calculateReadingTime(100,
            wordsPerPage: 300, wordsPerMinute: 250);
        expect(customReadingTime, greaterThan(0));
      });
    });

    group('Text Processing', () {
      test('should generate excerpt correctly', () {
        const longText = 'هذا نص طويل جداً يحتوي على عدة جمل. '
            'الجملة الثانية هنا. والجملة الثالثة أيضاً. '
            'المزيد من النص الطويل الذي يجب اختصاره.';

        final excerpt = Helpers.generateExcerpt(longText, maxLength: 50);
        expect(excerpt.length, lessThanOrEqualTo(53)); // 50 + '...'
        expect(excerpt, endsWith('...'));
      });

      test('should validate Arabic text correctly', () {
        expect(Helpers.isArabicText('مرحبا'), true);
        expect(Helpers.isArabicText('Hello'), false);
        expect(Helpers.isArabicText('مرحبا Hello'), true);
        expect(Helpers.isArabicText(''), false);
      });

      test('should format search query correctly', () {
        expect(Helpers.formatSearchQuery('  Hello World!  '), 'hello world');
        expect(Helpers.formatSearchQuery('مرحبا@#\$%'), 'مرحبا');
      });
    });

    group('Star Rating', () {
      test('should generate star rating correctly', () {
        final stars = Helpers.generateStarRating(4.5);
        expect(stars.length, 5);
        expect(stars.where((star) => star).length, 5); // All treated as full

        final lowStars = Helpers.generateStarRating(2.0);
        expect(lowStars.where((star) => star).length, 2);
      });
    });

    group('Review Count Formatting', () {
      test('should format review count in Arabic', () {
        expect(Helpers.formatReviewCount(0), 'لا توجد مراجعات');
        expect(Helpers.formatReviewCount(1), 'مراجعة واحدة');
        expect(Helpers.formatReviewCount(2), 'مراجعتان');
        expect(Helpers.formatReviewCount(5), '5 مراجعات');
        expect(Helpers.formatReviewCount(15), '15 مراجعة');
      });
    });
  });

  group('NetworkHelpers Tests', () {
    test('should validate URLs correctly', () {
      expect(NetworkHelpers.isValidUrl('https://example.com'), true);
      expect(NetworkHelpers.isValidUrl('http://example.com'), true);
      expect(NetworkHelpers.isValidUrl('ftp://example.com'), false);
      expect(NetworkHelpers.isValidUrl('invalid-url'), false);
    });

    test('should extract domain correctly', () {
      expect(NetworkHelpers.extractDomain('https://example.com/path'),
          'example.com');
      // Invalid URL returns empty string, not null
      final result = NetworkHelpers.extractDomain('invalid-url');
      expect(result, anyOf(isNull, equals('')));
    });

    test('should build query string correctly', () {
      final params = {'name': 'test', 'age': 25, 'active': true};
      final query = NetworkHelpers.buildQueryString(params);
      expect(query, startsWith('?'));
      expect(query, contains('name=test'));
      expect(query, contains('age=25'));
    });
  });

  group('ReadingHelpers Tests', () {
    test('should calculate reading speed correctly', () {
      final speed =
          ReadingHelpers.calculateReadingSpeed(200, const Duration(minutes: 1));
      expect(speed, 200.0);

      final zeroSpeed = ReadingHelpers.calculateReadingSpeed(
          100, const Duration(seconds: 30));
      expect(zeroSpeed, 0.0); // Less than 1 minute
    });

    test('should estimate word count correctly', () {
      expect(ReadingHelpers.estimateWordCount(''), 0);
      expect(ReadingHelpers.estimateWordCount('hello world'), 2);
      expect(ReadingHelpers.estimateWordCount('  hello   world  '), 2);
    });

    test('should calculate reading streak correctly', () {
      final today = DateTime.now();
      final yesterday = today.subtract(const Duration(days: 1));
      final twoDaysAgo = today.subtract(const Duration(days: 2));

      final streak = ReadingHelpers.calculateReadingStreak([
        today,
        yesterday,
        twoDaysAgo,
      ]);

      expect(streak, greaterThanOrEqualTo(0));
    });

    test('should generate reading goals correctly', () {
      final goals = ReadingHelpers.generateReadingGoals(10);
      expect(goals, isNotEmpty);
      expect(goals.every((goal) => goal > 10), true);
    });
  });

  group('UIHelpers Tests', () {
    test('should check RTL correctly', () {
      expect(UIHelpers.shouldUseRTL('مرحبا'), true);
      expect(UIHelpers.shouldUseRTL('Hello'), false);
    });

    test('should generate book cover color consistently', () {
      final color1 = UIHelpers.generateBookCoverColor('book1');
      final color2 = UIHelpers.generateBookCoverColor('book1');
      final color3 = UIHelpers.generateBookCoverColor('book2');

      expect(color1, equals(color2)); // Same ID = same color
      expect(color1,
          isNot(equals(color3))); // Different ID = likely different color
    });
  });

  group('String Extensions Tests', () {
    test('should check if string is null or empty', () {
      expect(''.isNullOrEmpty, true);
      expect('hello'.isNullOrEmpty, false);
    });

    test('should capitalize correctly', () {
      expect('hello'.capitalize, 'Hello');
      expect(''.capitalize, '');
      expect('HELLO'.capitalize, 'HELLO');
    });

    test('should trim correctly', () {
      expect('  hello   world  '.trimmed, 'hello world');
      expect('hello\n\nworld'.trimmed, 'hello world');
    });

    test('should validate email extension', () {
      expect('<EMAIL>'.isValidEmail, true);
      expect('invalid-email'.isValidEmail, false);
    });

    test('should check Arabic extension', () {
      expect('مرحبا'.isArabic, true);
      expect('Hello'.isArabic, false);
    });

    test('should format as search query extension', () {
      expect('  Hello World!  '.asSearchQuery, 'hello world');
    });

    test('should generate excerpt extension', () {
      const longText = 'This is a very long text that should be truncated.';
      final excerpt = longText.excerpt(maxLength: 20);
      expect(excerpt.length, lessThanOrEqualTo(23)); // 20 + '...'
      expect(excerpt, endsWith('...'));
    });
  });
}
