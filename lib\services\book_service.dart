import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/book.dart';
import '../models/review.dart';

class BookService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all books
  Future<List<Book>> getAllBooks() async {
    try {
      final querySnapshot = await _firestore.collection('books').get();
      return querySnapshot.docs
          .map((doc) => Book.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الكتب: ${e.toString()}');
    }
  }

  // Get books by category
  Future<List<Book>> getBooksByCategory(BookCategory category) async {
    try {
      final querySnapshot = await _firestore
          .collection('books')
          .where('category', isEqualTo: category.toString().split('.').last)
          .get();
      return querySnapshot.docs
          .map((doc) => Book.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الكتب حسب التصنيف: ${e.toString()}');
    }
  }

  // Get popular books
  Future<List<Book>> getPopularBooks() async {
    try {
      final querySnapshot = await _firestore
          .collection('books')
          .where('isPopular', isEqualTo: true)
          .orderBy('rating', descending: true)
          .limit(10)
          .get();
      return querySnapshot.docs
          .map((doc) => Book.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الكتب الشائعة: ${e.toString()}');
    }
  }

  // Get new books
  Future<List<Book>> getNewBooks() async {
    try {
      final querySnapshot = await _firestore
          .collection('books')
          .where('isNew', isEqualTo: true)
          .orderBy('publishedDate', descending: true)
          .limit(10)
          .get();
      return querySnapshot.docs
          .map((doc) => Book.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الكتب الجديدة: ${e.toString()}');
    }
  }

  // Get free books
  Future<List<Book>> getFreeBooks() async {
    try {
      final querySnapshot = await _firestore
          .collection('books')
          .where('isFree', isEqualTo: true)
          .get();
      return querySnapshot.docs
          .map((doc) => Book.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الكتب المجانية: ${e.toString()}');
    }
  }

  // Search books
  Future<List<Book>> searchBooks(String query) async {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation. For production, consider using Algolia or similar
      final querySnapshot = await _firestore.collection('books').get();
      final allBooks = querySnapshot.docs
          .map((doc) => Book.fromJson({...doc.data(), 'id': doc.id}))
          .toList();

      return allBooks
          .where((book) =>
              book.title.toLowerCase().contains(query.toLowerCase()) ||
              book.author.toLowerCase().contains(query.toLowerCase()) ||
              book.description.toLowerCase().contains(query.toLowerCase()))
          .toList();
    } catch (e) {
      throw Exception('فشل في البحث عن الكتب: ${e.toString()}');
    }
  }

  // Get book by ID
  Future<Book?> getBookById(String bookId) async {
    try {
      final doc = await _firestore.collection('books').doc(bookId).get();
      if (doc.exists) {
        return Book.fromJson({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب تفاصيل الكتاب: ${e.toString()}');
    }
  }

  // Get book reviews
  Future<List<Review>> getBookReviews(String bookId) async {
    try {
      final querySnapshot = await _firestore
          .collection('reviews')
          .where('bookId', isEqualTo: bookId)
          .orderBy('createdAt', descending: true)
          .get();
      return querySnapshot.docs
          .map((doc) => Review.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب مراجعات الكتاب: ${e.toString()}');
    }
  }

  // Add review
  Future<void> addReview(Review review) async {
    try {
      await _firestore.collection('reviews').add(review.toJson());

      // Update book rating
      await _updateBookRating(review.bookId);
    } catch (e) {
      throw Exception('فشل في إضافة المراجعة: ${e.toString()}');
    }
  }

  // Update book rating based on reviews
  Future<void> _updateBookRating(String bookId) async {
    try {
      final reviews = await getBookReviews(bookId);
      if (reviews.isNotEmpty) {
        final totalRating =
            reviews.fold<double>(0, (sum, review) => sum + review.rating);
        final averageRating = totalRating / reviews.length;

        await _firestore.collection('books').doc(bookId).update({
          'rating': averageRating,
          'reviewCount': reviews.length,
        });
      }
    } catch (e) {
      // Handle error silently as this is an internal operation
    }
  }

  // Get recommended books (basic implementation)
  Future<List<Book>> getRecommendedBooks(
      List<String> favoriteCategories) async {
    try {
      if (favoriteCategories.isEmpty) {
        return await getPopularBooks();
      }

      final querySnapshot = await _firestore
          .collection('books')
          .where('category', whereIn: favoriteCategories)
          .orderBy('rating', descending: true)
          .limit(10)
          .get();
      return querySnapshot.docs
          .map((doc) => Book.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الكتب المقترحة: ${e.toString()}');
    }
  }

  // Add book to favorites
  Future<void> addToFavorites(String userId, String bookId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'favoriteBooks': FieldValue.arrayUnion([bookId]),
      });
    } catch (e) {
      throw Exception('فشل في إضافة الكتاب للمفضلة: ${e.toString()}');
    }
  }

  // Remove book from favorites
  Future<void> removeFromFavorites(String userId, String bookId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'favoriteBooks': FieldValue.arrayRemove([bookId]),
      });
    } catch (e) {
      throw Exception('فشل في إزالة الكتاب من المفضلة: ${e.toString()}');
    }
  }
}
