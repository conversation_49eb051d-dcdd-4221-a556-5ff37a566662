import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/book_provider.dart';
import '../../models/book.dart';
import '../../widgets/book_card.dart';

class BookListScreen extends StatefulWidget {
  const BookListScreen({super.key});

  @override
  State<BookListScreen> createState() => _BookListScreenState();
}

class _BookListScreenState extends State<BookListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  BookCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showCategoryFilter() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختر التصنيف',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildCategoryChip(null, 'جميع التصنيفات'),
                ...BookCategory.values.map((category) =>
                    _buildCategoryChip(category, _getCategoryName(category))),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChip(BookCategory? category, String label) {
    final isSelected = _selectedCategory == category;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedCategory = selected ? category : null;
        });
        Navigator.of(context).pop();
      },
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  String _getCategoryName(BookCategory category) {
    switch (category) {
      case BookCategory.literature:
        return 'أدب';
      case BookCategory.science:
        return 'علوم';
      case BookCategory.novels:
        return 'روايات';
      case BookCategory.history:
        return 'تاريخ';
      case BookCategory.religion:
        return 'دين';
      case BookCategory.philosophy:
        return 'فلسفة';
      case BookCategory.poetry:
        return 'شعر';
      case BookCategory.children:
        return 'أطفال';
      case BookCategory.biography:
        return 'سيرة ذاتية';
      case BookCategory.selfDevelopment:
        return 'تطوير الذات';
    }
  }

  List<Book> _getFilteredBooks(List<Book> books) {
    if (_selectedCategory == null) {
      return books;
    }
    return books.where((book) => book.category == _selectedCategory).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الكتب'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showCategoryFilter,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'الكل'),
            Tab(text: 'الشائعة'),
            Tab(text: 'الجديدة'),
            Tab(text: 'المجانية'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllBooksTab(),
          _buildPopularBooksTab(),
          _buildNewBooksTab(),
          _buildFreeBooksTab(),
        ],
      ),
    );
  }

  Widget _buildAllBooksTab() {
    return Consumer<BookProvider>(
      builder: (context, bookProvider, child) {
        return FutureBuilder<List<Book>>(
          future: bookProvider.loadAllBooks().then((_) => bookProvider.allBooks),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return _buildErrorWidget(snapshot.error.toString());
            }

            final books = _getFilteredBooks(bookProvider.allBooks);
            return _buildBooksGrid(books);
          },
        );
      },
    );
  }

  Widget _buildPopularBooksTab() {
    return Consumer<BookProvider>(
      builder: (context, bookProvider, child) {
        final books = _getFilteredBooks(bookProvider.popularBooks);
        return _buildBooksGrid(books);
      },
    );
  }

  Widget _buildNewBooksTab() {
    return Consumer<BookProvider>(
      builder: (context, bookProvider, child) {
        final books = _getFilteredBooks(bookProvider.newBooks);
        return _buildBooksGrid(books);
      },
    );
  }

  Widget _buildFreeBooksTab() {
    return Consumer<BookProvider>(
      builder: (context, bookProvider, child) {
        final books = _getFilteredBooks(bookProvider.freeBooks);
        return _buildBooksGrid(books);
      },
    );
  }

  Widget _buildBooksGrid(List<Book> books) {
    if (books.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.library_books_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد كتب',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _selectedCategory != null
                  ? 'لا توجد كتب في هذا التصنيف'
                  : 'لا توجد كتب متاحة حالياً',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        final bookProvider = Provider.of<BookProvider>(context, listen: false);
        await bookProvider.refreshData();
      },
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.6,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: books.length,
        itemBuilder: (context, index) {
          return BookCard(
            book: books[index],
            width: double.infinity,
          );
        },
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              final bookProvider = Provider.of<BookProvider>(context, listen: false);
              bookProvider.refreshData();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}
