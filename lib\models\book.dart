enum BookFormat { pdf, epub, mobi }

enum BookCategory {
  literature,
  science,
  novels,
  history,
  religion,
  philosophy,
  poetry,
  children,
  biography,
  selfDevelopment
}

class Book {
  final String id;
  final String title;
  final String author;
  final String description;
  final String coverImage;
  final BookFormat format;
  final BookCategory category;
  final double price;
  final double rating;
  final int reviewCount;
  final String downloadUrl;
  final String previewUrl;
  final DateTime publishedDate;
  final int pageCount;
  final String language;
  final String publisher;
  final bool isFree;
  final bool isPopular;
  final bool isNew;

  Book({
    required this.id,
    required this.title,
    required this.author,
    required this.description,
    required this.coverImage,
    required this.format,
    required this.category,
    required this.price,
    this.rating = 0.0,
    this.reviewCount = 0,
    required this.downloadUrl,
    required this.previewUrl,
    required this.publishedDate,
    required this.pageCount,
    this.language = 'ar',
    required this.publisher,
    this.isFree = false,
    this.isPopular = false,
    this.isNew = false,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      author: json['author'] ?? '',
      description: json['description'] ?? '',
      coverImage: json['coverImage'] ?? '',
      format: BookFormat.values.firstWhere(
        (e) => e.toString().split('.').last == json['format'],
        orElse: () => BookFormat.pdf,
      ),
      category: BookCategory.values.firstWhere(
        (e) => e.toString().split('.').last == json['category'],
        orElse: () => BookCategory.literature,
      ),
      price: (json['price'] ?? 0.0).toDouble(),
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      downloadUrl: json['downloadUrl'] ?? '',
      previewUrl: json['previewUrl'] ?? '',
      publishedDate: DateTime.parse(json['publishedDate'] ?? DateTime.now().toIso8601String()),
      pageCount: json['pageCount'] ?? 0,
      language: json['language'] ?? 'ar',
      publisher: json['publisher'] ?? '',
      isFree: json['isFree'] ?? false,
      isPopular: json['isPopular'] ?? false,
      isNew: json['isNew'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'description': description,
      'coverImage': coverImage,
      'format': format.toString().split('.').last,
      'category': category.toString().split('.').last,
      'price': price,
      'rating': rating,
      'reviewCount': reviewCount,
      'downloadUrl': downloadUrl,
      'previewUrl': previewUrl,
      'publishedDate': publishedDate.toIso8601String(),
      'pageCount': pageCount,
      'language': language,
      'publisher': publisher,
      'isFree': isFree,
      'isPopular': isPopular,
      'isNew': isNew,
    };
  }

  String get categoryName {
    switch (category) {
      case BookCategory.literature:
        return 'أدب';
      case BookCategory.science:
        return 'علوم';
      case BookCategory.novels:
        return 'روايات';
      case BookCategory.history:
        return 'تاريخ';
      case BookCategory.religion:
        return 'دين';
      case BookCategory.philosophy:
        return 'فلسفة';
      case BookCategory.poetry:
        return 'شعر';
      case BookCategory.children:
        return 'أطفال';
      case BookCategory.biography:
        return 'سيرة ذاتية';
      case BookCategory.selfDevelopment:
        return 'تطوير الذات';
    }
  }
}
