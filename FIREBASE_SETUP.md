# إعداد Firebase لتطبيق إقرأ

هذا الدليل يوضح كيفية إعداد Firebase لتطبيق إقرأ خطوة بخطوة.

## 1. إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. انقر على "إضافة مشروع" (Add project)
3. أدخل اسم المشروع: `iqraa-app`
4. اختر إعدادات Google Analytics (اختياري)
5. انقر على "إنشاء مشروع"

## 2. إعداد Authentication

1. في لوحة تحكم Firebase، اذهب إلى **Authentication**
2. انقر على **Get started**
3. اذهب إلى تبويب **Sign-in method**
4. فعّل **Email/Password**:
   - انقر على Email/Password
   - فعّل "Enable"
   - احفظ التغييرات

## 3. إعداد Firestore Database

1. اذهب إلى **Firestore Database**
2. انقر على **Create database**
3. اختر **Start in test mode** (للتطوير)
4. اختر موقع قاعدة البيانات (اختر الأقرب لك)
5. انقر على **Done**

### إنشاء Collections الأساسية:

#### Collection: books
```javascript
// مثال على وثيقة كتاب
{
  title: "عنوان الكتاب",
  author: "اسم المؤلف", 
  description: "وصف الكتاب",
  coverImage: "https://example.com/cover.jpg",
  format: "pdf", // pdf, epub, mobi
  category: "literature", // literature, science, novels, etc.
  price: 29.99,
  rating: 4.5,
  reviewCount: 150,
  downloadUrl: "https://example.com/book.pdf",
  previewUrl: "https://example.com/preview.pdf",
  publishedDate: "2024-01-01T00:00:00Z",
  pageCount: 200,
  language: "ar",
  publisher: "دار النشر",
  isFree: false,
  isPopular: true,
  isNew: false
}
```

#### Collection: users
```javascript
// سيتم إنشاؤها تلقائياً عند التسجيل
{
  email: "<EMAIL>",
  name: "اسم المستخدم",
  profileImage: null,
  createdAt: "2024-01-01T00:00:00Z",
  favoriteBooks: [],
  purchasedBooks: [],
  readingProgress: {},
  isPremium: false
}
```

#### Collection: reviews
```javascript
// مثال على مراجعة
{
  bookId: "book_id_here",
  userId: "user_id_here", 
  userName: "اسم المستخدم",
  rating: 5,
  comment: "مراجعة ممتازة",
  createdAt: "2024-01-01T00:00:00Z"
}
```

## 4. إعداد Storage

1. اذهب إلى **Storage**
2. انقر على **Get started**
3. اختر **Start in test mode**
4. اختر موقع التخزين
5. انقر على **Done**

### إنشاء مجلدات التخزين:
- `books/` - لملفات الكتب
- `covers/` - لأغلفة الكتب
- `previews/` - لمعاينات الكتب
- `profiles/` - لصور الملفات الشخصية

## 5. إعداد التطبيق للأندرويد

1. في Firebase Console، انقر على أيقونة Android
2. أدخل package name: `com.iqraa.iqraa`
3. أدخل App nickname: `Iqraa Android`
4. حمّل ملف `google-services.json`
5. ضع الملف في `android/app/`

## 6. إعداد التطبيق للـ iOS

1. في Firebase Console، انقر على أيقونة iOS
2. أدخل Bundle ID: `com.iqraa.iqraa`
3. أدخل App nickname: `Iqraa iOS`
4. حمّل ملف `GoogleService-Info.plist`
5. ضع الملف في `ios/Runner/`

## 7. تحديث firebase_options.dart

بعد إضافة التطبيقات، احصل على إعدادات Firebase:

1. اذهب إلى **Project Settings** (⚙️)
2. انتقل إلى تبويب **General**
3. في قسم **Your apps**، انقر على **Config** لكل تطبيق
4. انسخ القيم وحدّث `lib/firebase_options.dart`:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'your-android-api-key-here',
  appId: 'your-android-app-id-here', 
  messagingSenderId: 'your-sender-id-here',
  projectId: 'your-project-id-here',
  storageBucket: 'your-project-id.appspot.com',
);

static const FirebaseOptions ios = FirebaseOptions(
  apiKey: 'your-ios-api-key-here',
  appId: 'your-ios-app-id-here',
  messagingSenderId: 'your-sender-id-here', 
  projectId: 'your-project-id-here',
  storageBucket: 'your-project-id.appspot.com',
  iosBundleId: 'com.iqraa.iqraa',
);
```

## 8. قواعد الأمان (Security Rules)

### Firestore Rules:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Anyone can read books
    match /books/{bookId} {
      allow read: if true;
      allow write: if false; // Only admins should write books
    }
    
    // Users can read all reviews, write their own
    match /reviews/{reviewId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Users can read their own notifications
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Users can read their own payments
    match /payments/{paymentId} {
      allow read: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null;
    }
  }
}
```

### Storage Rules:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Books - read only for authenticated users
    match /books/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only admins
    }
    
    // Covers - public read
    match /covers/{allPaths=**} {
      allow read: if true;
      allow write: if false; // Only admins
    }
    
    // Previews - public read  
    match /previews/{allPaths=**} {
      allow read: if true;
      allow write: if false; // Only admins
    }
    
    // Profile images - users can manage their own
    match /profiles/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && 
        request.auth.uid == userId;
    }
  }
}
```

## 9. إضافة بيانات تجريبية

يمكنك إضافة بعض الكتب التجريبية في Firestore:

```javascript
// في collection "books"
{
  title: "الأسود يليق بك",
  author: "أحلام مستغانمي",
  description: "رواية عربية معاصرة",
  coverImage: "https://example.com/cover1.jpg",
  format: "pdf",
  category: "novels", 
  price: 25.00,
  rating: 4.2,
  reviewCount: 89,
  downloadUrl: "https://example.com/book1.pdf",
  previewUrl: "https://example.com/preview1.pdf",
  publishedDate: "2023-01-15T00:00:00Z",
  pageCount: 320,
  language: "ar",
  publisher: "دار الآداب",
  isFree: false,
  isPopular: true,
  isNew: false
}
```

## 10. اختبار الإعداد

1. شغّل التطبيق: `flutter run`
2. جرّب تسجيل حساب جديد
3. تأكد من ظهور المستخدم في Firebase Console
4. جرّب تصفح الكتب (إذا أضفت بيانات تجريبية)

## ملاحظات مهمة

- **للإنتاج**: غيّر قواعد Firestore من test mode إلى production mode
- **الأمان**: لا تشارك مفاتيح API في الكود المفتوح
- **النسخ الاحتياطي**: فعّل النسخ الاحتياطي لـ Firestore
- **المراقبة**: فعّل Firebase Analytics و Crashlytics

## استكشاف الأخطاء

### خطأ: "Firebase project not found"
- تأكد من صحة project ID في firebase_options.dart

### خطأ: "Permission denied"  
- تحقق من قواعد الأمان في Firestore/Storage

### خطأ: "Network error"
- تأكد من اتصال الإنترنت
- تحقق من إعدادات الشبكة في المحاكي

---

بعد إكمال هذه الخطوات، سيكون تطبيق إقرأ جاهزاً للعمل مع Firebase!
