import 'package:flutter/foundation.dart';
import '../models/book.dart';
import '../models/review.dart';
import '../services/book_service.dart';

class BookProvider with ChangeNotifier {
  final BookService _bookService = BookService();

  List<Book> _allBooks = [];
  List<Book> _popularBooks = [];
  List<Book> _newBooks = [];
  List<Book> _freeBooks = [];
  List<Book> _searchResults = [];
  List<Review> _currentBookReviews = [];

  bool _isLoading = false;
  bool _isSearching = false;
  String? _error;
  String _searchQuery = '';

  // Getters
  List<Book> get allBooks => _allBooks;
  List<Book> get popularBooks => _popularBooks;
  List<Book> get newBooks => _newBooks;
  List<Book> get freeBooks => _freeBooks;
  List<Book> get searchResults => _searchResults;
  List<Review> get currentBookReviews => _currentBookReviews;
  bool get isLoading => _isLoading;
  bool get isSearching => _isSearching;
  String? get error => _error;
  String get searchQuery => _searchQuery;

  // Initialize data
  Future<void> initializeData() async {
    _setLoading(true);
    try {
      await Future.wait([
        loadPopularBooks(),
        loadNewBooks(),
        loadFreeBooks(),
      ]);
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load all books
  Future<void> loadAllBooks() async {
    try {
      _allBooks = await _bookService.getAllBooks();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Load popular books
  Future<void> loadPopularBooks() async {
    try {
      _popularBooks = await _bookService.getPopularBooks();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Load new books
  Future<void> loadNewBooks() async {
    try {
      _newBooks = await _bookService.getNewBooks();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Load free books
  Future<void> loadFreeBooks() async {
    try {
      _freeBooks = await _bookService.getFreeBooks();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Load books by category
  Future<List<Book>> loadBooksByCategory(BookCategory category) async {
    try {
      return await _bookService.getBooksByCategory(category);
    } catch (e) {
      _setError(e.toString());
      return [];
    }
  }

  // Search books
  Future<void> searchBooks(String query) async {
    if (query.trim().isEmpty) {
      _searchResults = [];
      _searchQuery = '';
      notifyListeners();
      return;
    }

    _setSearching(true);
    _searchQuery = query;

    try {
      _searchResults = await _bookService.searchBooks(query);
    } catch (e) {
      _setError(e.toString());
      _searchResults = [];
    } finally {
      _setSearching(false);
    }
  }

  // Clear search
  void clearSearch() {
    _searchResults = [];
    _searchQuery = '';
    notifyListeners();
  }

  // Get book by ID
  Future<Book?> getBookById(String bookId) async {
    try {
      return await _bookService.getBookById(bookId);
    } catch (e) {
      _setError(e.toString());
      return null;
    }
  }

  // Load book reviews
  Future<void> loadBookReviews(String bookId) async {
    try {
      _currentBookReviews = await _bookService.getBookReviews(bookId);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Add review
  Future<bool> addReview(Review review) async {
    try {
      await _bookService.addReview(review);
      // Reload reviews to get updated list
      await loadBookReviews(review.bookId);
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Get recommended books
  Future<List<Book>> getRecommendedBooks(
      List<String> favoriteCategories) async {
    try {
      return await _bookService.getRecommendedBooks(favoriteCategories);
    } catch (e) {
      _setError(e.toString());
      return [];
    }
  }

  // Filter books by category
  List<Book> filterBooksByCategory(List<Book> books, BookCategory category) {
    return books.where((book) => book.category == category).toList();
  }

  // Sort books
  List<Book> sortBooks(List<Book> books, String sortBy) {
    final sortedBooks = List<Book>.from(books);

    switch (sortBy) {
      case 'title':
        sortedBooks.sort((a, b) => a.title.compareTo(b.title));
        break;
      case 'author':
        sortedBooks.sort((a, b) => a.author.compareTo(b.author));
        break;
      case 'rating':
        sortedBooks.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'price':
        sortedBooks.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'date':
        sortedBooks.sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
        break;
      default:
        break;
    }

    return sortedBooks;
  }

  // Refresh data
  Future<void> refreshData() async {
    await initializeData();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setSearching(bool searching) {
    _isSearching = searching;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
