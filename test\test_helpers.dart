import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:iqraa/providers/auth_provider.dart';
import 'package:iqraa/providers/book_provider.dart';
import 'package:iqraa/models/book.dart';
import 'package:iqraa/models/user.dart' as app_user;

/// Helper function to create a test app with providers
Widget createTestApp({
  required Widget child,
  AuthProvider? authProvider,
  BookProvider? bookProvider,
}) {
  return MultiProvider(
    providers: [
      ChangeNotifierProvider<AuthProvider>(
        create: (_) => authProvider ?? AuthProvider(),
      ),
      ChangeNotifierProvider<BookProvider>(
        create: (_) => bookProvider ?? BookProvider(),
      ),
    ],
    child: MaterialApp(
      home: child,
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'Cairo',
      ),
    ),
  );
}

/// Helper function to pump a widget with providers
Future<void> pumpWidgetWithProviders(
  WidgetTester tester,
  Widget widget, {
  AuthProvider? authProvider,
  BookProvider? bookProvider,
}) async {
  await tester.pumpWidget(
    createTestApp(
      child: widget,
      authProvider: authProvider,
      bookProvider: bookProvider,
    ),
  );
}

/// Mock data for testing
class TestData {
  // User Data
  static const String testEmail = '<EMAIL>';
  static const String testPassword = 'password123';
  static const String testUserName = 'Test User';
  static const String testUserNameArabic = 'مستخدم تجريبي';
  static const String testUserId = 'test-user-123';

  // Book Data
  static const String testBookTitle = 'كتاب تجريبي';
  static const String testAuthor = 'مؤلف تجريبي';
  static const String testBookId = 'book-123';
  static const String testBookDescription = 'وصف الكتاب التجريبي';
  static const String testPublisher = 'دار النشر التجريبية';
  static const double testBookPrice = 25.99;
  static const double testBookRating = 4.5;
  static const int testReviewCount = 150;
  static const int testPageCount = 300;

  // URLs
  static const String testCoverImageUrl = 'https://example.com/cover.jpg';
  static const String testDownloadUrl = 'https://example.com/book.pdf';
  static const String testPreviewUrl = 'https://example.com/preview.pdf';

  // Error Messages
  static const String testErrorMessage = 'حدث خطأ غير متوقع';
  static const String testSuccessMessage = 'تمت العملية بنجاح';

  // Search and Filter
  static const String testSearchQuery = 'رواية';
  static const String testCategoryFilter = 'الأدب';

  // Payment
  static const String testCardNumber = '****************';
  static const String testCardExpiry = '12/25';
  static const String testCardCvc = '123';
  static const String testCardHolderName = 'Test User';

  // Reading Progress
  static const int testCurrentPage = 50;
  static const int testTotalPages = 200;
  static const double testReadingProgress = 0.25;

  // Dates
  static final DateTime testPublishedDate = DateTime(2024, 1, 1);
  static final DateTime testPurchaseDate = DateTime(2024, 2, 1);
  static final DateTime testLastReadDate = DateTime(2024, 2, 15);

  // Lists
  static const List<String> testTags = ['أدب', 'رواية', 'خيال'];
  static const List<String> testLanguages = ['ar', 'en'];

  // Settings
  static const double testFontSize = 16.0;
  static const double testLineHeight = 1.5;
  static const String testFontFamily = 'Cairo';
  static const bool testDarkMode = false;

  // Navigation
  static const String testRouteName = '/home';
  static const int testBottomNavIndex = 0;
}

/// Factory for creating test models
class TestModelFactory {
  /// Create a test book
  static Book createTestBook({
    String? id,
    String? title,
    String? author,
    double? price,
    double? rating,
  }) {
    return Book(
      id: id ?? TestData.testBookId,
      title: title ?? TestData.testBookTitle,
      author: author ?? TestData.testAuthor,
      description: TestData.testBookDescription,
      coverImage: TestData.testCoverImageUrl,
      format: BookFormat.pdf,
      category: BookCategory.literature,
      price: price ?? TestData.testBookPrice,
      rating: rating ?? TestData.testBookRating,
      reviewCount: TestData.testReviewCount,
      downloadUrl: TestData.testDownloadUrl,
      previewUrl: TestData.testPreviewUrl,
      publishedDate: TestData.testPublishedDate,
      pageCount: TestData.testPageCount,
      language: 'ar',
      publisher: TestData.testPublisher,
      isFree: false,
      isPopular: true,
    );
  }

  /// Create a test user
  static app_user.User createTestUser({
    String? id,
    String? email,
    String? name,
    bool? isPremium,
  }) {
    return app_user.User(
      id: id ?? TestData.testUserId,
      email: email ?? TestData.testEmail,
      name: name ?? TestData.testUserName,
      profileImage: null,
      createdAt: TestData.testPurchaseDate,
      readingProgress: const {},
      favoriteBooks: const [],
      purchasedBooks: const [],
      isPremium: isPremium ?? false,
    );
  }

  /// Create a list of test books
  static List<Book> createTestBookList(int count) {
    return List.generate(count, (index) {
      return createTestBook(
        id: 'book-$index',
        title: '${TestData.testBookTitle} $index',
        author: '${TestData.testAuthor} $index',
        price: TestData.testBookPrice + index,
        rating: (TestData.testBookRating - 0.1 * index).clamp(1.0, 5.0),
      );
    });
  }

  /// Create a free book
  static Book createFreeBook() {
    return Book(
      id: 'free-book-1',
      title: 'كتاب مجاني',
      author: TestData.testAuthor,
      description: 'كتاب مجاني للقراءة',
      coverImage: TestData.testCoverImageUrl,
      format: BookFormat.pdf,
      category: BookCategory.literature,
      price: 0.0,
      rating: 4.0,
      reviewCount: 50,
      downloadUrl: TestData.testDownloadUrl,
      previewUrl: TestData.testPreviewUrl,
      publishedDate: TestData.testPublishedDate,
      pageCount: 150,
      language: 'ar',
      publisher: TestData.testPublisher,
      isFree: true,
      isPopular: false,
    );
  }

  /// Create a popular book
  static Book createPopularBook() {
    return Book(
      id: 'popular-book-1',
      title: 'كتاب شائع',
      author: TestData.testAuthor,
      description: 'كتاب شائع ومميز',
      coverImage: TestData.testCoverImageUrl,
      format: BookFormat.epub,
      category: BookCategory.novels,
      price: 35.0,
      rating: 4.8,
      reviewCount: 500,
      downloadUrl: TestData.testDownloadUrl,
      previewUrl: TestData.testPreviewUrl,
      publishedDate: TestData.testPublishedDate,
      pageCount: 400,
      language: 'ar',
      publisher: TestData.testPublisher,
      isFree: false,
      isPopular: true,
    );
  }
}

/// Custom matchers for testing
class CustomMatchers {
  static Matcher hasText(String text) => findsAtLeastNWidgets(1);
  static Matcher hasIcon(IconData icon) => findsAtLeastNWidgets(1);
  static Matcher hasWidget<T>() => findsAtLeastNWidgets(1);

  /// Check if text exists
  static bool textExists(String text) {
    return find.text(text).evaluate().isNotEmpty;
  }

  /// Check if icon exists
  static bool iconExists(IconData icon) {
    return find.byIcon(icon).evaluate().isNotEmpty;
  }

  /// Check if widget type exists
  static bool widgetExists<T>() {
    return find.byType(T).evaluate().isNotEmpty;
  }
}

/// Test utilities
class TestUtils {
  /// Wait for animations to complete
  static Future<void> waitForAnimations(WidgetTester tester) async {
    await tester.pumpAndSettle();
  }

  /// Simulate user input
  static Future<void> enterText(
    WidgetTester tester,
    String text, {
    String? fieldKey,
  }) async {
    final finder =
        fieldKey != null ? find.byKey(Key(fieldKey)) : find.byType(TextField);

    await tester.enterText(finder, text);
    await tester.pump();
  }

  /// Simulate button tap
  static Future<void> tapButton(
    WidgetTester tester, {
    String? buttonText,
    String? buttonKey,
    Type? buttonType,
  }) async {
    late Finder finder;

    if (buttonText != null) {
      finder = find.text(buttonText);
    } else if (buttonKey != null) {
      finder = find.byKey(Key(buttonKey));
    } else if (buttonType != null) {
      finder = find.byType(buttonType);
    } else {
      finder = find.byType(ElevatedButton);
    }

    await tester.tap(finder);
    await tester.pump();
  }

  /// Verify error message is displayed
  static void expectErrorMessage(String message) {
    expect(find.text(message), findsOneWidget);
  }

  /// Verify success message is displayed
  static void expectSuccessMessage(String message) {
    expect(find.text(message), findsOneWidget);
  }

  /// Verify loading indicator is displayed
  static void expectLoadingIndicator() {
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  }

  /// Verify navigation to specific screen
  static void expectScreen<T>() {
    expect(find.byType(T), findsOneWidget);
  }

  /// Scroll to find a widget
  static Future<void> scrollToFind(
    WidgetTester tester,
    Finder finder, {
    Finder? scrollable,
  }) async {
    final scrollableFinder = scrollable ?? find.byType(Scrollable);
    await tester.scrollUntilVisible(finder, 100.0,
        scrollable: scrollableFinder);
  }

  /// Wait for a specific duration
  static Future<void> wait(Duration duration) async {
    await Future.delayed(duration);
  }

  /// Simulate long press
  static Future<void> longPress(
    WidgetTester tester,
    Finder finder,
  ) async {
    await tester.longPress(finder);
    await tester.pump();
  }

  /// Simulate drag gesture
  static Future<void> drag(
    WidgetTester tester,
    Finder finder,
    Offset offset,
  ) async {
    await tester.drag(finder, offset);
    await tester.pump();
  }

  /// Take screenshot (for debugging)
  static Future<void> takeScreenshot(
    WidgetTester tester,
    String name,
  ) async {
    // This would be implemented with actual screenshot functionality
    // For now, it's a placeholder
    debugPrint('📸 Screenshot taken: $name');
  }

  /// Verify widget has specific property
  static void expectWidgetProperty<T>(
    WidgetTester tester,
    Finder finder,
    String property,
    dynamic expectedValue,
  ) {
    // This would need reflection or specific property checks
    // For now, just verify the widget exists
    expect(finder, findsOneWidget);
  }

  /// Get current route name
  static String? getCurrentRoute(WidgetTester tester) {
    // This would extract the current route
    // For now, return null as placeholder
    return null;
  }

  /// Verify specific text style
  static void expectTextStyle(
    String text,
    TextStyle expectedStyle,
  ) {
    final textWidget = find.text(text);
    expect(textWidget, findsOneWidget);
    // Additional style verification would go here
  }

  /// Simulate device back button
  static Future<void> pressBackButton(WidgetTester tester) async {
    await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
      'flutter/platform',
      null,
      (data) {},
    );
  }

  /// Verify app bar title
  static void expectAppBarTitle(String title) {
    expect(
        find.descendant(
          of: find.byType(AppBar),
          matching: find.text(title),
        ),
        findsOneWidget);
  }

  /// Verify bottom navigation item is selected
  static void expectBottomNavSelected(int index) {
    final bottomNav = find.byType(BottomNavigationBar);
    expect(bottomNav, findsOneWidget);
    // Additional verification for selected index would go here
  }
}
