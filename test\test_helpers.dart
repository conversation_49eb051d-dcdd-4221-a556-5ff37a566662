import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:iqraa/providers/auth_provider.dart';
import 'package:iqraa/providers/book_provider.dart';

/// Helper function to create a test app with providers
Widget createTestApp({
  required Widget child,
  AuthProvider? authProvider,
  BookProvider? bookProvider,
}) {
  return MultiProvider(
    providers: [
      ChangeNotifierProvider<AuthProvider>(
        create: (_) => authProvider ?? AuthProvider(),
      ),
      ChangeNotifierProvider<BookProvider>(
        create: (_) => bookProvider ?? BookProvider(),
      ),
    ],
    child: MaterialApp(
      home: child,
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'Cairo',
      ),
    ),
  );
}

/// Helper function to pump a widget with providers
Future<void> pumpWidgetWithProviders(
  WidgetTester tester,
  Widget widget, {
  AuthProvider? authProvider,
  BookProvider? bookProvider,
}) async {
  await tester.pumpWidget(
    createTestApp(
      child: widget,
      authProvider: authProvider,
      bookProvider: bookProvider,
    ),
  );
}

/// Mock data for testing
class TestData {
  static const String testEmail = '<EMAIL>';
  static const String testPassword = 'password123';
  static const String testUserName = 'Test User';
  static const String testBookTitle = 'كتاب تجريبي';
  static const String testAuthor = 'مؤلف تجريبي';
}

/// Custom matchers for testing
class CustomMatchers {
  static Matcher hasText(String text) => findsWidgets(find.text(text));
  static Matcher hasIcon(IconData icon) => findsWidgets(find.byIcon(icon));
  static Matcher hasWidget<T>() => findsWidgets(find.byType(T));
}

/// Test utilities
class TestUtils {
  /// Wait for animations to complete
  static Future<void> waitForAnimations(WidgetTester tester) async {
    await tester.pumpAndSettle();
  }

  /// Simulate user input
  static Future<void> enterText(
    WidgetTester tester,
    String text, {
    String? fieldKey,
  }) async {
    final finder = fieldKey != null 
        ? find.byKey(Key(fieldKey))
        : find.byType(TextField);
    
    await tester.enterText(finder, text);
    await tester.pump();
  }

  /// Simulate button tap
  static Future<void> tapButton(
    WidgetTester tester, {
    String? buttonText,
    String? buttonKey,
    Type? buttonType,
  }) async {
    late Finder finder;
    
    if (buttonText != null) {
      finder = find.text(buttonText);
    } else if (buttonKey != null) {
      finder = find.byKey(Key(buttonKey));
    } else if (buttonType != null) {
      finder = find.byType(buttonType);
    } else {
      finder = find.byType(ElevatedButton);
    }
    
    await tester.tap(finder);
    await tester.pump();
  }

  /// Verify error message is displayed
  static void expectErrorMessage(String message) {
    expect(find.text(message), findsOneWidget);
  }

  /// Verify success message is displayed
  static void expectSuccessMessage(String message) {
    expect(find.text(message), findsOneWidget);
  }

  /// Verify loading indicator is displayed
  static void expectLoadingIndicator() {
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  }

  /// Verify navigation to specific screen
  static void expectScreen<T>() {
    expect(find.byType(T), findsOneWidget);
  }
}
