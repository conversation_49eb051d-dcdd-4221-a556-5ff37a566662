import 'package:flutter_test/flutter_test.dart';
import 'package:iqraa/models/book.dart';

void main() {
  group('Simple Tests', () {
    test('should create a book model', () {
      final book = Book(
        id: '1',
        title: 'كتاب تجريبي',
        author: 'مؤلف تجريبي',
        description: 'وصف الكتاب',
        coverImage: 'https://example.com/cover.jpg',
        format: BookFormat.pdf,
        category: BookCategory.literature,
        price: 25.0,
        rating: 4.5,
        reviewCount: 100,
        downloadUrl: 'https://example.com/book.pdf',
        previewUrl: 'https://example.com/preview.pdf',
        publishedDate: DateTime(2024, 1, 1),
        pageCount: 200,
        language: 'ar',
        publisher: 'دار النشر التجريبية',
        isFree: false,
        isPopular: true,
      );

      expect(book.id, '1');
      expect(book.title, 'كتاب تجريبي');
      expect(book.format, BookFormat.pdf);
      expect(book.category, BookCategory.literature);
    });

    test('should verify enum values', () {
      expect(BookFormat.pdf, isNotNull);
      expect(BookFormat.epub, isNotNull);
      expect(BookCategory.literature, isNotNull);
      expect(BookCategory.science, isNotNull);
    });

    test('should handle basic math', () {
      expect(2 + 2, 4);
      expect(10 / 2, 5);
    });
  });
}
