import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:iqraa/main_simple.dart';

void main() {
  group('App Integration Tests', () {
    testWidgets('should navigate through main app flow',
        (WidgetTester tester) async {
      // Build the simple app
      await tester.pumpWidget(const SimpleIqraaApp());

      // Verify app starts with home screen
      expect(find.text('مرحباً بك في تطبيق إقرأ'), findsOneWidget);
      expect(find.text('✅ تم إنشاء التطبيق بنجاح!'), findsOneWidget);

      // Test bottom navigation
      expect(find.byType(BottomNavigationBar), findsOneWidget);

      // Navigate to Books tab
      await tester.tap(find.text('الكتب'));
      await tester.pumpAndSettle();

      expect(find.text('قائمة الكتب'), findsOneWidget);
      expect(
          find.text('سيتم عرض الكتب هنا بعد إعداد Firebase'), findsOneWidget);

      // Navigate to Library tab
      await tester.tap(find.text('مكتبتي'));
      await tester.pumpAndSettle();

      expect(find.text('مكتبتي الشخصية'), findsOneWidget);
      expect(find.text('ستظهر كتبك المشتراة هنا'), findsOneWidget);

      // Navigate to Profile tab
      await tester.tap(find.text('الملف الشخصي'));
      await tester.pumpAndSettle();

      expect(find.text('مستخدم تجريبي'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);

      // Navigate back to Home tab
      await tester.tap(find.text('الرئيسية'));
      await tester.pumpAndSettle();

      expect(find.text('مرحباً بك في تطبيق إقرأ'), findsOneWidget);
    });

    testWidgets('should display correct app bar titles',
        (WidgetTester tester) async {
      await tester.pumpWidget(const SimpleIqraaApp());

      // Check home screen app bar
      expect(find.text('إقرأ'), findsOneWidget);

      // Navigate to books and check app bar
      await tester.tap(find.text('الكتب'));
      await tester.pumpAndSettle();
      expect(find.text('الكتب'), findsAtLeastNWidgets(1));

      // Navigate to library and check app bar
      await tester.tap(find.text('مكتبتي'));
      await tester.pumpAndSettle();
      expect(find.text('مكتبتي'), findsAtLeastNWidgets(1));

      // Navigate to profile and check app bar
      await tester.tap(find.text('الملف الشخصي'));
      await tester.pumpAndSettle();
      expect(find.text('الملف الشخصي'), findsAtLeastNWidgets(1));
    });

    testWidgets('should display correct icons in bottom navigation',
        (WidgetTester tester) async {
      await tester.pumpWidget(const SimpleIqraaApp());

      // Verify all navigation icons are present
      expect(find.byIcon(Icons.home), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.book), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.library_books), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.person), findsAtLeastNWidgets(1));
    });

    testWidgets('should maintain selected tab state',
        (WidgetTester tester) async {
      await tester.pumpWidget(const SimpleIqraaApp());

      // Start on home tab (index 0)
      final bottomNavBar = tester.widget<BottomNavigationBar>(
        find.byType(BottomNavigationBar),
      );
      expect(bottomNavBar.currentIndex, 0);

      // Navigate to books tab (index 1)
      await tester.tap(find.text('الكتب'));
      await tester.pumpAndSettle();

      final updatedBottomNavBar = tester.widget<BottomNavigationBar>(
        find.byType(BottomNavigationBar),
      );
      expect(updatedBottomNavBar.currentIndex, 1);
    });

    testWidgets('should display placeholder content for each tab',
        (WidgetTester tester) async {
      await tester.pumpWidget(const SimpleIqraaApp());

      // Home tab content
      expect(find.byIcon(Icons.book), findsAtLeastNWidgets(1));
      expect(
          find.text('تطبيق قراءة الكتب الإلكترونية العربية'), findsOneWidget);

      // Books tab content
      await tester.tap(find.text('الكتب'));
      await tester.pumpAndSettle();
      expect(find.byIcon(Icons.book_outlined), findsOneWidget);

      // Library tab content
      await tester.tap(find.text('مكتبتي'));
      await tester.pumpAndSettle();
      expect(find.byIcon(Icons.library_books_outlined), findsOneWidget);

      // Profile tab content
      await tester.tap(find.text('الملف الشخصي'));
      await tester.pumpAndSettle();
      expect(find.byType(CircleAvatar), findsOneWidget);
      expect(find.byIcon(Icons.person), findsAtLeastNWidgets(1));
    });
  });
}
